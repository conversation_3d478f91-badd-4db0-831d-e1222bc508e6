#!/usr/bin/env python3
"""
Run baseline test with open-source Lance for comparison
"""

import os
import sys
import time
import pandas as pd
import numpy as np
import pyarrow as pa
import lance
from pathlib import Path
import gc

def clear_cache():
    """Clear system and Python caches"""
    gc.collect()

def create_test_dataset(num_rows=100000, output_path="test_spatial_data.lance"):
    """Create a test spatial dataset"""
    print(f"Creating test dataset with {num_rows:,} rows...")
    
    # Generate realistic NYC taxi-like data (same as GeoPage test)
    np.random.seed(42)
    
    # NYC bounding box (roughly)
    lon_min, lon_max = -74.05, -73.75
    lat_min, lat_max = 40.63, 40.85
    
    data = {
        'pickup_longitude': np.random.uniform(lon_min, lon_max, num_rows),
        'pickup_latitude': np.random.uniform(lat_min, lat_max, num_rows),
        'dropoff_longitude': np.random.uniform(lon_min, lon_max, num_rows),
        'dropoff_latitude': np.random.uniform(lat_min, lat_max, num_rows),
        'trip_distance': np.random.exponential(2.0, num_rows),
        'fare_amount': np.random.exponential(10.0, num_rows),
        'passenger_count': np.random.randint(1, 7, num_rows),
    }
    
    df = pd.DataFrame(data)
    table = pa.Table.from_pandas(df)
    
    # Remove existing dataset
    if os.path.exists(output_path):
        import shutil
        shutil.rmtree(output_path)
    
    # Write with Lance
    lance.write_dataset(table, output_path)
    print(f"Dataset created: {output_path}")
    return output_path

def run_spatial_queries(dataset_path):
    """Run spatial queries and measure performance"""
    print(f"Running spatial queries on {dataset_path}...")
    
    # Same test bounding boxes as GeoPage test
    test_bboxes = [
        # Small boxes (should benefit most from spatial indexing)
        (-74.01, 40.75, -74.00, 40.76),  # Times Square area
        (-73.99, 40.74, -73.98, 40.75),  # East Village
        (-73.97, 40.76, -73.96, 40.77),  # Upper East Side
        
        # Medium boxes
        (-74.02, 40.74, -74.00, 40.76),  # Midtown West
        (-73.99, 40.73, -73.97, 40.75),  # Lower Manhattan
        
        # Large boxes (less benefit from spatial indexing)
        (-74.05, 40.70, -74.00, 40.75),  # Large Manhattan area
        (-74.02, 40.75, -73.95, 40.80),  # Large Midtown to Upper Manhattan
    ]
    
    results = []
    dataset = lance.dataset(dataset_path)
    
    for i, (xmin, ymin, xmax, ymax) in enumerate(test_bboxes):
        clear_cache()
        
        start_time = time.time()
        
        # Spatial filter query
        filtered = dataset.to_table(
            filter=f"pickup_longitude >= {xmin} AND pickup_longitude <= {xmax} AND "
                  f"pickup_latitude >= {ymin} AND pickup_latitude <= {ymax}"
        )
        
        query_time = time.time() - start_time
        
        result_count = len(filtered)
        bbox_area = (xmax - xmin) * (ymax - ymin)
        
        results.append({
            'query_id': i,
            'bbox': (xmin, ymin, xmax, ymax),
            'bbox_area': bbox_area,
            'result_count': result_count,
            'query_time_ms': query_time * 1000,
        })
        
        print(f"  Query {i+1}: {result_count:,} results in {query_time*1000:.1f}ms")
    
    return results

def run_baseline_test():
    """Run baseline test with open-source Lance"""
    print("📦 Open-source Lance Baseline Test")
    print("=" * 50)
    print(f"Lance version: {lance.__version__}")
    print(f"Current environment: {os.environ.get('VIRTUAL_ENV', 'system')}")
    
    # Test parameters (same as GeoPage test)
    test_sizes = [50000, 100000, 200000]
    
    all_results = []
    
    for size in test_sizes:
        print(f"\n📊 Testing with {size:,} rows")
        print("-" * 30)
        
        # Create test dataset
        dataset_path = f"baseline_spatial_{size}.lance"
        create_test_dataset(size, dataset_path)
        
        # Run spatial queries
        results = run_spatial_queries(dataset_path)
        
        for result in results:
            result['lance_version'] = 'opensource_baseline'
            result['dataset_size'] = size
            all_results.append(result)
        
        # Cleanup
        if os.path.exists(dataset_path):
            import shutil
            shutil.rmtree(dataset_path)
    
    return all_results

def analyze_baseline_results(results):
    """Analyze baseline results"""
    df = pd.DataFrame(results)
    
    print("\n📈 Baseline Results Analysis")
    print("=" * 50)
    
    # Group by dataset size and calculate metrics
    summary = df.groupby('dataset_size').agg({
        'query_time_ms': ['mean', 'std', 'min', 'max'],
        'result_count': 'mean'
    }).round(2)
    
    print("\n⏱️  Query Performance Summary:")
    print(summary['query_time_ms'])
    
    # Spatial selectivity analysis
    print("\n🎯 Spatial Selectivity Analysis:")
    spatial_analysis = df.groupby('bbox_area').agg({
        'query_time_ms': 'mean',
        'result_count': 'mean'
    }).round(2)
    
    print("Query time by bbox area:")
    print(spatial_analysis['query_time_ms'])
    
    return df

def main():
    """Main baseline test execution"""
    print("🌍 Open-source Lance Baseline Performance Test")
    
    # Run the baseline test
    results = run_baseline_test()
    
    # Analyze results
    results_df = analyze_baseline_results(results)
    
    # Save results
    results_df.to_csv('baseline_test_results.csv', index=False)
    print(f"\n💾 Results saved to: baseline_test_results.csv")
    
    print("\n✅ Baseline Test Complete!")
    print("\nNext steps:")
    print("1. Compare with geopage_ab_test_results.csv")
    print("2. Calculate actual performance improvements")

if __name__ == "__main__":
    main()
