#!/usr/bin/env python3
"""
Comprehensive WKB integration tests with real-world mimicking data.
Tests Point, LineString, and Polygon geometries with the Lance WKB utilities.
"""

import os
import sys
import time
import json
import struct
import pandas as pd
import pyarrow as pa
import lance
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Any
import numpy as np

class WKBIntegrationTester:
    def __init__(self):
        self.results = {}
        
    def create_wkb_point(self, lon: float, lat: float, big_endian: bool = False) -> bytes:
        """Create WKB Point geometry."""
        endian = b'\x00' if big_endian else b'\x01'  # 0 = big endian, 1 = little endian
        geom_type = struct.pack('<I' if not big_endian else '>I', 1)  # Point = 1
        x = struct.pack('<d' if not big_endian else '>d', lon)
        y = struct.pack('<d' if not big_endian else '>d', lat)
        return endian + geom_type + x + y
    
    def create_wkb_linestring(self, coords: List[Tuple[float, float]], big_endian: bool = False) -> bytes:
        """Create WKB LineString geometry."""
        endian = b'\x00' if big_endian else b'\x01'
        geom_type = struct.pack('<I' if not big_endian else '>I', 2)  # LineString = 2
        num_points = struct.pack('<I' if not big_endian else '>I', len(coords))
        
        points_data = b''
        for lon, lat in coords:
            x = struct.pack('<d' if not big_endian else '>d', lon)
            y = struct.pack('<d' if not big_endian else '>d', lat)
            points_data += x + y
            
        return endian + geom_type + num_points + points_data
    
    def create_wkb_polygon(self, exterior_ring: List[Tuple[float, float]], 
                          interior_rings: List[List[Tuple[float, float]]] = None,
                          big_endian: bool = False) -> bytes:
        """Create WKB Polygon geometry."""
        if interior_rings is None:
            interior_rings = []
            
        endian = b'\x00' if big_endian else b'\x01'
        geom_type = struct.pack('<I' if not big_endian else '>I', 3)  # Polygon = 3
        num_rings = struct.pack('<I' if not big_endian else '>I', 1 + len(interior_rings))
        
        # Exterior ring
        ring_data = b''
        num_points = struct.pack('<I' if not big_endian else '>I', len(exterior_ring))
        ring_data += num_points
        
        for lon, lat in exterior_ring:
            x = struct.pack('<d' if not big_endian else '>d', lon)
            y = struct.pack('<d' if not big_endian else '>d', lat)
            ring_data += x + y
        
        # Interior rings (holes)
        for interior_ring in interior_rings:
            num_points = struct.pack('<I' if not big_endian else '>I', len(interior_ring))
            ring_data += num_points
            
            for lon, lat in interior_ring:
                x = struct.pack('<d' if not big_endian else '>d', lon)
                y = struct.pack('<d' if not big_endian else '>d', lat)
                ring_data += x + y
                
        return endian + geom_type + num_rings + ring_data

    def generate_nyc_points(self, count: int = 1000) -> pd.DataFrame:
        """Generate realistic NYC taxi pickup/dropoff points."""
        print(f"🗽 Generating {count:,} realistic NYC taxi points...")
        
        # NYC bounding box (Manhattan focus)
        nyc_bounds = {
            'min_lon': -74.02, 'max_lon': -73.93,
            'min_lat': 40.70, 'max_lat': 40.80
        }
        
        # Generate clustered points around popular areas
        hotspots = [
            (-73.9857, 40.7484),  # Times Square
            (-73.9776, 40.7505),  # Penn Station
            (-73.9712, 40.7831),  # Central Park
            (-74.0059, 40.7128),  # Financial District
            (-73.9442, 40.8176),  # Harlem
        ]
        
        points = []
        wkb_geometries = []
        
        for i in range(count):
            # 70% of points clustered around hotspots, 30% random
            if np.random.random() < 0.7:
                # Pick a random hotspot and add noise
                hotspot = hotspots[np.random.randint(len(hotspots))]
                lon = hotspot[0] + np.random.normal(0, 0.01)  # ~1km radius
                lat = hotspot[1] + np.random.normal(0, 0.01)
            else:
                # Random point in NYC bounds
                lon = np.random.uniform(nyc_bounds['min_lon'], nyc_bounds['max_lon'])
                lat = np.random.uniform(nyc_bounds['min_lat'], nyc_bounds['max_lat'])
            
            # Create WKB Point
            use_big_endian = np.random.random() < 0.1  # 10% big endian for testing
            wkb_point = self.create_wkb_point(lon, lat, big_endian=use_big_endian)
            
            points.append({
                'point_id': i,
                'longitude': lon,
                'latitude': lat,
                'wkb_geometry': wkb_point,
                'geometry_type': 'Point',
                'endianness': 'big' if use_big_endian else 'little',
                'trip_distance': np.random.exponential(2.0),  # Realistic trip distance
                'fare_amount': np.random.gamma(2, 3),  # Realistic fare
            })
            
        df = pd.DataFrame(points)
        print(f"✅ Generated {len(df):,} NYC taxi points")
        return df

    def generate_nyc_routes(self, count: int = 500) -> pd.DataFrame:
        """Generate realistic NYC taxi routes as LineStrings."""
        print(f"🛣️ Generating {count:,} realistic NYC taxi routes...")
        
        routes = []
        
        for i in range(count):
            # Generate route with 3-8 waypoints
            num_waypoints = np.random.randint(3, 9)
            
            # Start point (pickup)
            start_lon = np.random.uniform(-74.02, -73.93)
            start_lat = np.random.uniform(40.70, 40.80)
            
            # Generate route waypoints with realistic movement
            route_coords = [(start_lon, start_lat)]
            
            current_lon, current_lat = start_lon, start_lat
            for _ in range(num_waypoints - 1):
                # Move in a somewhat realistic direction (not too random)
                direction = np.random.uniform(0, 2 * np.pi)
                distance = np.random.exponential(0.005)  # ~500m average step
                
                current_lon += distance * np.cos(direction)
                current_lat += distance * np.sin(direction)
                
                # Keep within NYC bounds
                current_lon = np.clip(current_lon, -74.02, -73.93)
                current_lat = np.clip(current_lat, 40.70, 40.80)
                
                route_coords.append((current_lon, current_lat))
            
            # Create WKB LineString
            use_big_endian = np.random.random() < 0.1
            wkb_linestring = self.create_wkb_linestring(route_coords, big_endian=use_big_endian)
            
            routes.append({
                'route_id': i,
                'start_lon': start_lon,
                'start_lat': start_lat,
                'end_lon': current_lon,
                'end_lat': current_lat,
                'waypoint_count': len(route_coords),
                'wkb_geometry': wkb_linestring,
                'geometry_type': 'LineString',
                'endianness': 'big' if use_big_endian else 'little',
                'route_distance': sum(
                    ((route_coords[i+1][0] - route_coords[i][0])**2 + 
                     (route_coords[i+1][1] - route_coords[i][1])**2)**0.5 
                    for i in range(len(route_coords)-1)
                ),
            })
        
        df = pd.DataFrame(routes)
        print(f"✅ Generated {len(df):,} NYC taxi routes")
        return df

    def generate_nyc_zones(self, count: int = 200) -> pd.DataFrame:
        """Generate realistic NYC zones as Polygons."""
        print(f"🏙️ Generating {count:,} realistic NYC zones...")
        
        zones = []
        
        for i in range(count):
            # Generate polygon with 4-8 vertices
            num_vertices = np.random.randint(4, 9)
            
            # Center point for the zone
            center_lon = np.random.uniform(-74.01, -73.94)
            center_lat = np.random.uniform(40.71, 40.79)
            
            # Generate polygon vertices around center
            vertices = []
            for j in range(num_vertices):
                angle = (2 * np.pi * j) / num_vertices + np.random.normal(0, 0.2)
                radius = np.random.uniform(0.002, 0.008)  # 200-800m radius
                
                vertex_lon = center_lon + radius * np.cos(angle)
                vertex_lat = center_lat + radius * np.sin(angle)
                vertices.append((vertex_lon, vertex_lat))
            
            # Close the polygon (first point = last point)
            vertices.append(vertices[0])
            
            # Create WKB Polygon
            use_big_endian = np.random.random() < 0.1
            wkb_polygon = self.create_wkb_polygon(vertices, big_endian=use_big_endian)
            
            # Calculate approximate area
            area = 0.0
            for j in range(len(vertices) - 1):
                area += vertices[j][0] * vertices[j+1][1] - vertices[j+1][0] * vertices[j][1]
            area = abs(area) / 2.0
            
            zones.append({
                'zone_id': i,
                'center_lon': center_lon,
                'center_lat': center_lat,
                'vertex_count': num_vertices,
                'wkb_geometry': wkb_polygon,
                'geometry_type': 'Polygon',
                'endianness': 'big' if use_big_endian else 'little',
                'area_approx': area,
                'zone_type': np.random.choice(['residential', 'commercial', 'mixed', 'industrial']),
            })
        
        df = pd.DataFrame(zones)
        print(f"✅ Generated {len(df):,} NYC zones")
        return df

    def test_wkb_lance_integration(self, df: pd.DataFrame, test_name: str) -> Dict[str, Any]:
        """Test WKB data integration with Lance."""
        print(f"\n🧪 Testing {test_name} WKB integration with Lance...")

        results = {
            'test_name': test_name,
            'data_info': {
                'rows': len(df),
                'columns': len(df.columns),
                'geometry_types': df['geometry_type'].value_counts().to_dict(),
                'endianness_distribution': df['endianness'].value_counts().to_dict(),
            }
        }

        # Create dataset path
        dataset_path = Path(f"datasets/wkb_test_{test_name.lower()}")
        dataset_path.parent.mkdir(exist_ok=True)

        if dataset_path.exists():
            shutil.rmtree(dataset_path)

        # Test 1: Write performance with WKB data
        print("📝 Testing WKB write performance...")
        write_start = time.time()

        try:
            # Write with GeoPage encoding for WKB column
            dataset = lance.write_dataset(df, dataset_path, encoding="geopage")
            write_time = time.time() - write_start

            # Get dataset size
            dataset_size = sum(f.stat().st_size for f in dataset_path.rglob('*') if f.is_file())
            dataset_size_mb = dataset_size / (1024 * 1024)

            results['write_performance'] = {
                'time_seconds': write_time,
                'dataset_size_mb': dataset_size_mb,
                'write_success': True,
            }

            print(f"✅ Write successful: {write_time:.3f}s, {dataset_size_mb:.2f}MB")

        except Exception as e:
            results['write_performance'] = {
                'error': str(e),
                'write_success': False,
            }
            print(f"❌ Write failed: {e}")
            return results

        # Test 2: Read performance
        print("📖 Testing WKB read performance...")
        read_start = time.time()

        try:
            read_data = dataset.to_table().to_pandas()
            read_time = time.time() - read_start

            results['read_performance'] = {
                'time_seconds': read_time,
                'rows_read': len(read_data),
                'read_success': True,
                'data_integrity': len(read_data) == len(df),
            }

            print(f"✅ Read successful: {read_time:.3f}s, {len(read_data):,} rows")

        except Exception as e:
            results['read_performance'] = {
                'error': str(e),
                'read_success': False,
            }
            print(f"❌ Read failed: {e}")

        # Test 3: WKB column filtering (if possible)
        print("🔍 Testing WKB column access...")
        try:
            wkb_column = dataset.to_table(columns=['wkb_geometry']).to_pandas()

            results['wkb_column_access'] = {
                'success': True,
                'wkb_column_size': len(wkb_column),
                'sample_wkb_length': len(wkb_column['wkb_geometry'].iloc[0]) if len(wkb_column) > 0 else 0,
            }

            print(f"✅ WKB column access successful: {len(wkb_column):,} geometries")

        except Exception as e:
            results['wkb_column_access'] = {
                'error': str(e),
                'success': False,
            }
            print(f"❌ WKB column access failed: {e}")

        return results

    def test_wkb_lance_integration(self, df: pd.DataFrame, test_name: str) -> Dict[str, Any]:
        """Test WKB data integration with Lance."""
        print(f"\n🧪 Testing {test_name} WKB integration with Lance...")

        results = {
            'test_name': test_name,
            'data_info': {
                'rows': len(df),
                'columns': len(df.columns),
                'geometry_types': df['geometry_type'].value_counts().to_dict(),
                'endianness_distribution': df['endianness'].value_counts().to_dict(),
            }
        }

        # Create dataset path
        dataset_path = Path(f"datasets/wkb_test_{test_name.lower()}")
        dataset_path.parent.mkdir(exist_ok=True)

        if dataset_path.exists():
            shutil.rmtree(dataset_path)

        # Test 1: Write performance with WKB data
        print("📝 Testing WKB write performance...")
        write_start = time.time()

        try:
            # Write with GeoPage encoding for WKB column
            dataset = lance.write_dataset(df, dataset_path, encoding="geopage")
            write_time = time.time() - write_start

            # Get dataset size
            dataset_size = sum(f.stat().st_size for f in dataset_path.rglob('*') if f.is_file())
            dataset_size_mb = dataset_size / (1024 * 1024)

            results['write_performance'] = {
                'time_seconds': write_time,
                'dataset_size_mb': dataset_size_mb,
                'write_success': True,
            }

            print(f"✅ Write successful: {write_time:.3f}s, {dataset_size_mb:.2f}MB")

        except Exception as e:
            results['write_performance'] = {
                'error': str(e),
                'write_success': False,
            }
            print(f"❌ Write failed: {e}")
            return results

        # Test 2: Read performance
        print("📖 Testing WKB read performance...")
        read_start = time.time()

        try:
            read_data = dataset.to_table().to_pandas()
            read_time = time.time() - read_start

            results['read_performance'] = {
                'time_seconds': read_time,
                'rows_read': len(read_data),
                'read_success': True,
                'data_integrity': len(read_data) == len(df),
            }

            print(f"✅ Read successful: {read_time:.3f}s, {len(read_data):,} rows")

        except Exception as e:
            results['read_performance'] = {
                'error': str(e),
                'read_success': False,
            }
            print(f"❌ Read failed: {e}")

        # Test 3: WKB column filtering (if possible)
        print("🔍 Testing WKB column access...")
        try:
            wkb_column = dataset.to_table(columns=['wkb_geometry']).to_pandas()

            results['wkb_column_access'] = {
                'success': True,
                'wkb_column_size': len(wkb_column),
                'sample_wkb_length': len(wkb_column['wkb_geometry'].iloc[0]) if len(wkb_column) > 0 else 0,
            }

            print(f"✅ WKB column access successful: {len(wkb_column):,} geometries")

        except Exception as e:
            results['wkb_column_access'] = {
                'error': str(e),
                'success': False,
            }
            print(f"❌ WKB column access failed: {e}")

        return results

    def run_comprehensive_wkb_tests(self) -> Dict[str, Any]:
        """Run comprehensive WKB integration tests."""
        print("🚀 Starting comprehensive WKB integration tests...")

        all_results = {
            'test_suite': 'WKB Integration Tests',
            'timestamp': time.time(),
            'tests': {}
        }

        # Test 1: NYC Points
        points_df = self.generate_nyc_points(count=2000)
        points_results = self.test_wkb_lance_integration(points_df, "Points")
        all_results['tests']['points'] = points_results

        # Test 2: NYC Routes (LineStrings)
        routes_df = self.generate_nyc_routes(count=1000)
        routes_results = self.test_wkb_lance_integration(routes_df, "Routes")
        all_results['tests']['routes'] = routes_results

        # Test 3: NYC Zones (Polygons)
        zones_df = self.generate_nyc_zones(count=500)
        zones_results = self.test_wkb_lance_integration(zones_df, "Zones")
        all_results['tests']['zones'] = zones_results

        # Test 4: Mixed geometries
        print("\n🔀 Creating mixed geometry dataset...")
        mixed_df = pd.concat([
            points_df.head(500),
            routes_df.head(300),
            zones_df.head(200)
        ], ignore_index=True)

        mixed_results = self.test_wkb_lance_integration(mixed_df, "Mixed")
        all_results['tests']['mixed'] = mixed_results

        return all_results

    def save_results(self, results: Dict[str, Any], output_dir: str = "test_results"):
        """Save test results to JSON file."""
        os.makedirs(output_dir, exist_ok=True)
        timestamp = int(time.time())
        filename = f"wkb_integration_results_{timestamp}.json"
        filepath = Path(output_dir) / filename

        with open(filepath, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        print(f"💾 Results saved to: {filepath}")
        return filepath

def main():
    print("=" * 80)
    print("🗺️ WKB INTEGRATION TESTING SUITE")
    print("=" * 80)

    tester = WKBIntegrationTester()

    # Run comprehensive tests
    results = tester.run_comprehensive_wkb_tests()

    # Save results
    results_file = tester.save_results(results)

    # Print summary
    print("\n" + "=" * 80)
    print("📊 WKB INTEGRATION TEST SUMMARY")
    print("=" * 80)

    for test_name, test_results in results['tests'].items():
        print(f"\n🧪 {test_name.upper()} TEST:")

        if test_results.get('write_performance', {}).get('write_success'):
            write_perf = test_results['write_performance']
            print(f"  ✅ Write: {write_perf['time_seconds']:.3f}s, {write_perf['dataset_size_mb']:.2f}MB")
        else:
            print(f"  ❌ Write failed")

        if test_results.get('read_performance', {}).get('read_success'):
            read_perf = test_results['read_performance']
            print(f"  ✅ Read: {read_perf['time_seconds']:.3f}s, {read_perf['rows_read']:,} rows")
        else:
            print(f"  ❌ Read failed")

        if test_results.get('wkb_column_access', {}).get('success'):
            wkb_access = test_results['wkb_column_access']
            print(f"  ✅ WKB access: {wkb_access['wkb_column_size']:,} geometries")
        else:
            print(f"  ❌ WKB access failed")

    print(f"\n💾 Detailed results saved to: {results_file}")
    print("🎉 WKB integration testing complete!")

    return results

if __name__ == "__main__":
    results = main()
