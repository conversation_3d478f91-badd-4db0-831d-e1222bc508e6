#!/usr/bin/env python3
"""
Convenience script to run A/B tests comparing open-source Lance vs custom Lance with GeoPage.
This script runs both modes and compares the results.
"""

import subprocess
import sys
import json
import time
from pathlib import Path

def drop_caches():
    """Drop OS page caches to ensure fair benchmarking."""
    try:
        # Try to drop caches (requires sudo, may fail)
        result = subprocess.run(
            ["sudo", "sh", "-c", "echo 3 > /proc/sys/vm/drop_caches"],
            capture_output=True,
            text=True,
            timeout=5
        )
        if result.returncode == 0:
            print("✅ Dropped OS page caches for fair benchmarking")
            return True
        else:
            print("🟡 Could not drop caches (no sudo), results may show warm cache effects")
            return False
    except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
        print("🟡 Could not drop caches, results may show warm cache effects")
        return False

def run_test(mode: str, max_rows: int = None, data_file: str = None, drop_cache: bool = True):
    """Run a single test mode."""
    print(f"\n{'='*80}")
    print(f"🚀 RUNNING {mode.upper()} MODE TEST")
    print(f"{'='*80}")

    # Drop caches before each test for fair comparison
    if drop_cache:
        drop_caches()

    cmd = ["python", "test_uber_ab_comparison.py", "--mode", mode]

    if max_rows:
        cmd.extend(["--max-rows", str(max_rows)])

    if data_file:
        cmd.extend(["--data-file", data_file])

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Test failed: {e}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False

def compare_results():
    """Compare the most recent results from both modes."""
    print(f"\n{'='*80}")
    print("📊 COMPARING RESULTS")
    print(f"{'='*80}")
    
    results_dir = Path("test_results")
    if not results_dir.exists():
        print("❌ No test results found")
        return
    
    # Find most recent results for each mode
    opensource_files = sorted(results_dir.glob("uber_ab_results_opensource_*.json"))
    custom_files = sorted(results_dir.glob("uber_ab_results_custom_*.json"))
    
    if not opensource_files or not custom_files:
        print("❌ Missing results for comparison")
        print(f"  Open source results: {len(opensource_files)}")
        print(f"  Custom results: {len(custom_files)}")
        return
    
    # Load most recent results
    with open(opensource_files[-1]) as f:
        opensource_results = json.load(f)
    
    with open(custom_files[-1]) as f:
        custom_results = json.load(f)
    
    print(f"📁 Comparing:")
    print(f"  Open source: {opensource_files[-1].name}")
    print(f"  Custom:      {custom_files[-1].name}")
    
    # Compare key metrics
    print(f"\n📊 PERFORMANCE COMPARISON")
    print(f"{'Metric':<30} {'Open Source':<15} {'Custom':<15} {'Improvement':<15}")
    print("-" * 75)
    
    # Write performance
    os_write = opensource_results['write_performance']['time_seconds']
    custom_write = custom_results['write_performance']['time_seconds']
    write_improvement = ((os_write - custom_write) / os_write) * 100
    print(f"{'Write time (s)':<30} {os_write:<15.3f} {custom_write:<15.3f} {write_improvement:+.1f}%")
    
    # Full scan performance
    os_scan = opensource_results['full_scan_performance']['time_seconds']
    custom_scan = custom_results['full_scan_performance']['time_seconds']
    scan_improvement = ((os_scan - custom_scan) / os_scan) * 100
    print(f"{'Full scan time (s)':<30} {os_scan:<15.3f} {custom_scan:<15.3f} {scan_improvement:+.1f}%")
    
    # Dataset size
    os_size = opensource_results['write_performance']['dataset_size_mb']
    custom_size = custom_results['write_performance']['dataset_size_mb']
    size_change = ((custom_size - os_size) / os_size) * 100
    print(f"{'Dataset size (MB)':<30} {os_size:<15.2f} {custom_size:<15.2f} {size_change:+.1f}%")
    
    # Spatial query comparison (if available)
    if (opensource_results['spatial_query_performance'] and 
        custom_results['spatial_query_performance']):
        
        print(f"\n🗺️ SPATIAL QUERY COMPARISON")
        print(f"{'Query':<25} {'Open Source (s)':<15} {'Custom (s)':<15} {'Improvement':<15}")
        print("-" * 70)
        
        for os_query, custom_query in zip(opensource_results['spatial_query_performance'],
                                         custom_results['spatial_query_performance']):
            if 'error' not in os_query and 'error' not in custom_query:
                os_time = os_query['time_seconds']
                custom_time = custom_query['time_seconds']
                improvement = ((os_time - custom_time) / os_time) * 100
                print(f"{os_query['name']:<25} {os_time:<15.3f} {custom_time:<15.3f} {improvement:+.1f}%")
    
    # GeoPage-specific results
    if 'gap_analysis_validation' in custom_results:
        print(f"\n🎯 GEOPAGE VALIDATION RESULTS")
        gap_tests = custom_results['gap_analysis_validation']
        
        for test_name, test_result in gap_tests.items():
            if isinstance(test_result, dict) and 'error' not in test_result:
                print(f"  ✅ {test_name}: {test_result.get('status', 'completed')}")
            else:
                print(f"  ❌ {test_name}: failed")
    
    print(f"\n🎉 Comparison complete!")

def main():
    """Main function to run A/B tests."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run A/B tests for Lance GeoPage")
    parser.add_argument("--max-rows", type=int, default=100000,
                       help="Maximum rows to test (default: 100000)")
    parser.add_argument("--data-file", type=str,
                       default="uber_data/nyc-taxi/2009/01/data.parquet",
                       help="Path to test data file")
    parser.add_argument("--mode", choices=["opensource", "custom", "presorted", "both", "all"], default="both",
                       help="Which mode to run: opensource, custom, presorted, both (opensource+custom), or all (all three)")
    parser.add_argument("--compare-only", action="store_true",
                       help="Only compare existing results, don't run new tests")
    
    args = parser.parse_args()
    
    print("🚗 UBER DATA A/B TESTING SUITE")
    print(f"📊 Test size: {args.max_rows:,} rows")
    print(f"📁 Data file: {args.data_file}")
    
    if args.compare_only:
        compare_results()
        return
    
    success = True
    
    if args.mode in ["opensource", "both", "all"]:
        print("\n🔧 Testing with open-source Lance...")
        success &= run_test("opensource", args.max_rows, args.data_file)

    if args.mode in ["custom", "both", "all"]:
        print("\n🔧 Testing with custom Lance + GeoPage...")
        success &= run_test("custom", args.max_rows, args.data_file)

    if args.mode in ["presorted", "all"]:
        print("\n🔄 Testing with spatial pre-sorted Lance...")
        success &= run_test("presorted", args.max_rows, args.data_file)

    if success and args.mode in ["both", "all"]:
        compare_results()
    elif not success:
        print("❌ Some tests failed")
        sys.exit(1)
    
    print("\n🎉 All tests completed successfully!")

if __name__ == "__main__":
    main()
