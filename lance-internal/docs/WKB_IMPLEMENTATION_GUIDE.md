# WKB (Well-Known Binary) Support in Lance GeoPage Encoding

## Overview

This document describes the implementation of WKB (Well-Known Binary) geometry support for Lance's GeoPage spatial encoding system. The implementation enables <PERSON> to automatically detect and spatially optimize WKB geometry columns for efficient spatial queries on data from PostGIS, GeoParquet files, and other WKB-producing systems.

## Implementation Architecture

### Core Components

1. **WKB Parser Module** (`rust/lance-encoding/src/encodings/wkb_utils.rs`)
   - `SimpleWkbParser`: Core WKB parsing functionality
   - `SpatialPoint`: Spatial point representation for indexing
   - Support for Point, LineString, and Polygon geometries

2. **GeoPage Integration** (`rust/lance-encoding/src/encodings/geopage.rs`)
   - Enhanced geometry parsing to handle both WKT and WKB data
   - Automatic WKB detection and fallback to WKT parsing

3. **Encoder Detection** (`rust/lance-encoding/src/encoder.rs`)
   - Enhanced spatial column detection with GeoArrow extension type support
   - WKB data validation in binary columns

## Supported Geometry Types

The implementation focuses on core OGC geometry types essential for spatial indexing:

### Point (Type 1)
- **Strategy**: Extract coordinates directly
- **Use Case**: Direct spatial indexing of point locations
- **Example**: GPS coordinates, POI locations

### LineString (Type 2)  
- **Strategy**: Use first coordinate as representative point
- **Use Case**: Route optimization, path analysis
- **Example**: Road segments, flight paths

### Polygon (Type 3)
- **Strategy**: Calculate simple centroid from exterior ring
- **Use Case**: Area-based spatial queries
- **Example**: Administrative boundaries, land parcels

## Technical Implementation Details

### WKB Parsing Strategy

The implementation uses direct byte parsing for performance:

```rust
// WKB Structure: [endian][type][coordinates...]
let endianness = wkb_bytes[0]; // 0 = big endian, 1 = little endian
let geometry_type = read_u32(&wkb_bytes[1..5], endianness);
```

### Representative Point Extraction

For spatial indexing, each geometry type extracts a single representative point:

- **Point**: Direct coordinates `(x, y)`
- **LineString**: First point `points[0]`
- **Polygon**: Simple centroid `(Σx/n, Σy/n)` of exterior ring

### Coordinate Validation

All extracted coordinates are validated against WGS84 bounds:
- Longitude: `-180.0 ≤ lon ≤ 180.0`
- Latitude: `-90.0 ≤ lat ≤ 90.0`
- Finite values only (no NaN or infinity)

## Integration with Lance GeoPage

### Automatic Detection

The system automatically detects WKB data through multiple mechanisms:

1. **Extension Type Detection**:
   ```rust
   field.extension_name().contains("geoarrow.wkb")
   ```

2. **Column Name Patterns**:
   ```rust
   ["geom", "geometry", "wkb", "coordinates", ...]
   ```

3. **Binary Data Validation**:
   ```rust
   SimpleWkbParser::is_likely_wkb(binary_data)
   ```

### Processing Pipeline

1. **Detection**: Identify WKB columns during encoding
2. **Parsing**: Extract representative points from WKB geometries
3. **Indexing**: Generate spatial quadtree index for efficient queries
4. **Storage**: Store with spatial metadata for query optimization

## Performance Characteristics

### Parsing Performance
- **Direct byte parsing**: Avoids intermediate geometry representations
- **Minimal validation**: Only essential coordinate bounds checking
- **Early termination**: Representative point extraction stops after first valid point

### Memory Efficiency
- **Zero-copy parsing**: Works directly with input byte arrays
- **Minimal allocations**: Only for final coordinate results
- **Streaming compatible**: Processes geometries individually

### Spatial Optimization
- **SSD-level sorting**: Spatial keys for disk locality
- **Quadtree indexing**: Hierarchical spatial organization
- **Query pushdown**: Spatial filtering at storage level

## Usage Examples

### Python Integration

```python
import lance
import pandas as pd
from shapely.geometry import Point
from shapely import wkb

# Create dataset with WKB geometries
points = [Point(x, y) for x, y in [(1.0, 2.0), (3.0, 4.0)]]
wkb_data = [wkb.dumps(point) for point in points]

df = pd.DataFrame({
    'id': [1, 2],
    'geometry': wkb_data  # Binary WKB data
})

# Lance automatically detects WKB and applies GeoPage encoding
dataset = lance.write_dataset(df, "spatial_data.lance", 
                             encoding={"geometry": "geopage"})
```

### Spatial Queries

```python
# Spatial filtering with bounding box
results = dataset.to_table(
    filter="geometry INTERSECTS ST_MakeEnvelope(0, 0, 2, 3)"
).to_pandas()
```

## Extension Type Support

### GeoArrow Compatibility

The implementation supports standard GeoArrow extension types:

- `geoarrow.wkb`: Standard GeoArrow WKB extension
- `ogc.wkb`: OGC-compliant WKB extension

### Metadata Detection

```rust
// Check field metadata for spatial extension types
if let Some(ext_name) = field.extension_name() {
    if ext_name.contains("geoarrow") || ext_name.contains("wkb") {
        // Apply GeoPage encoding
    }
}
```

## Error Handling

### Graceful Degradation

The implementation provides robust error handling:

1. **Invalid WKB**: Falls back to WKT parsing
2. **Malformed geometries**: Skips invalid entries
3. **Coordinate validation**: Rejects out-of-bounds values
4. **Unsupported types**: Clear error messages

### Validation Levels

- **Structural**: WKB header and basic format validation
- **Geometric**: Coordinate bounds and validity checking
- **Spatial**: Representative point extraction verification

## Testing Strategy

### Unit Tests
- WKB parsing for all supported geometry types
- Endianness handling (little and big endian)
- Error conditions and edge cases
- Coordinate validation

### Integration Tests
- End-to-end GeoPage encoding with WKB data
- Spatial query performance validation
- Compatibility with existing coordinate pair workflows

### Real-World Data
- PostGIS export compatibility
- GeoParquet file processing
- Performance benchmarks with actual datasets

## Future Enhancements

### Planned Features
1. **Extended geometry types**: MultiPoint, MultiLineString, MultiPolygon
2. **3D coordinate support**: Z-dimension handling
3. **SRID preservation**: Spatial reference system metadata
4. **Advanced centroids**: Geometric vs. arithmetic centroid options

### Performance Optimizations
1. **SIMD acceleration**: Vectorized coordinate processing
2. **Parallel parsing**: Multi-threaded geometry processing
3. **Memory mapping**: Zero-copy file access
4. **Compression**: Spatial-aware compression schemes

## Compatibility

### Backward Compatibility
- Existing coordinate pair workflows unchanged
- WKT parsing still supported
- No breaking changes to Lance API

### Standards Compliance
- OGC WKB specification compliance
- GeoArrow extension type compatibility
- PostGIS interoperability

## Conclusion

The WKB support implementation provides Lance with industry-standard geometry format compatibility while maintaining high performance and seamless integration with existing spatial workflows. The focus on representative point extraction for spatial indexing ensures optimal storage-level optimization without compromising query functionality.
