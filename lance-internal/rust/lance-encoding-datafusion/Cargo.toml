[package]
name = "lance-encoding-datafusion"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true
readme = "README.md"
description = "Encoders and decoders for the Lance file format that rely on datafusion"
keywords.workspace = true
categories.workspace = true
rust-version.workspace = true

[dependencies]
lance-core = { workspace = true, features = ["datafusion"] }
lance-datafusion = { workspace = true, features = ["substrait"] }

lance-encoding.workspace = true
lance-file.workspace = true
lance-io.workspace = true
arrow-array.workspace = true
arrow-buffer.workspace = true
arrow-schema.workspace = true
bytes.workspace = true
datafusion.workspace = true
datafusion-common.workspace = true
datafusion-expr.workspace = true
datafusion-functions.workspace = true
datafusion-optimizer.workspace = true
datafusion-physical-expr.workspace = true
futures.workspace = true
log.workspace = true
prost.workspace = true
prost-types.workspace = true
snafu.workspace = true

[dev-dependencies]
rand.workspace = true
test-log.workspace = true
tokio.workspace = true
lance-datagen.workspace = true

[build-dependencies]
prost-build.workspace = true
protobuf-src = { version = "2.1", optional = true }

[target.'cfg(target_os = "linux")'.dev-dependencies]
pprof = { workspace = true }

[features]
protoc = ["dep:protobuf-src"]

[package.metadata.docs.rs]
# docs.rs uses an older version of Ubuntu that does not have the necessary protoc version
features = ["protoc"]

[lints]
workspace = true
