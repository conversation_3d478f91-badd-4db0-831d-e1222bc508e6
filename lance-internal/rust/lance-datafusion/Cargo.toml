[package]
name = "lance-datafusion"
version.workspace = true
edition.workspace = true
license.workspace = true
repository.workspace = true
readme.workspace = true
keywords.workspace = true
categories.workspace = true
description = "Internal utilities used by other lance modules to simplify working with datafusion"

[dependencies]
arrow = { workspace = true, features = ["ffi"] }
arrow-array = { workspace = true, features = ["ffi"] }
arrow-buffer.workspace = true
arrow-schema.workspace = true
arrow-select.workspace = true
arrow-ord.workspace = true
async-trait.workspace = true
datafusion.workspace = true
datafusion-common.workspace = true
datafusion-functions.workspace = true
datafusion-physical-expr.workspace = true
datafusion-substrait = { workspace = true, optional = true }
futures.workspace = true
lance-arrow.workspace = true
lance-core = { workspace = true, features = ["datafusion"] }
lance-datagen.workspace = true
lazy_static.workspace = true
log.workspace = true
pin-project.workspace = true
prost.workspace = true
snafu.workspace = true
tempfile.workspace = true
tokio.workspace = true
tracing.workspace = true

[dev-dependencies]
substrait-expr = { version = "0.2.3" }
lance-datagen.workspace = true

[features]
substrait = ["dep:datafusion-substrait"]

[lints]
workspace = true
