# Lance Rust Workspace

This directory contains the core Rust implementation of Lance, a modern columnar data format optimized for ML workflows.

## Crates

| Crate | Description |
|-------|-------------|
| `lance` | Main Lance library with high-level APIs |
| `lance-core` | Core data structures and utilities |
| `lance-encoding` | Data encoding/decoding including **GeoPage spatial codec** |
| `lance-file` | File format implementation |
| `lance-index` | Vector and secondary indices |
| `lance-io` | I/O abstractions and object store integration |
| `lance-table` | Table operations and query planning |
| `lance-datafusion` | DataFusion integration for SQL queries |
| `lance-arrow` | Arrow integration utilities |
| `lance-linalg` | Linear algebra operations |

## GeoPage Spatial Codec

The `lance-encoding` crate includes the **GeoPage codec** for efficient geospatial data processing:

### Features
- 🗺️ **Automatic spatial column detection** (lat/lon, geometry columns)
- 📊 **Quadtree spatial indexing** with Z-order curve data sorting
- ⚡ **Efficient spatial filtering** for range queries
- 🔍 **Multiple spatial filter formats** (SQL, PostGIS, bbox)
- 🏗️ **Zero configuration** - automatically applied to spatial data

### Implementation
- **Location**: `rust/lance-encoding/src/encodings/geopage.rs`
- **Protobuf**: `protos/geopage.proto`
- **Integration**: Automatic encoder selection in `rust/lance-encoding/src/encoder.rs`

### Testing
```bash
# Run GeoPage-specific tests
cargo test -p lance-encoding geopage --lib

# Run all encoding tests
cargo test -p lance-encoding --lib

# Performance benchmarks
cargo test -p lance-encoding geopage::test_spatial_performance_simulation --release
```

## Building

```bash
# Build all crates
cargo build --release

# Build specific crate
cargo build -p lance-encoding --release

# Run tests
cargo test --workspace

# Run with optimizations
cargo test --release
```

## Development

The Lance Rust codebase follows these patterns:
- **Encoders/Decoders**: Implement `ArrayEncoder`/`ArrayDecoder` traits
- **4KiB page alignment**: All data pages are aligned to 4KiB boundaries
- **Protobuf schemas**: Define encoding metadata in `protos/` directory
- **Lance patterns**: Follow existing encoder patterns for consistency

For GeoPage development, see:
- `docs/GEOPAGE_IMPLEMENTATION_GUIDE.md` - Implementation details
- `docs/GEOPAGE_TECHNICAL_DEEP_DIVE.md` - Technical architecture
- `docs/GEOPAGE_ARCHITECTURE.md` - High-level design
