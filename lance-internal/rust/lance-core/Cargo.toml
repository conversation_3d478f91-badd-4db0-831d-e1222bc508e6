[package]
name = "lance-core"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true
readme.workspace = true
description = "Lance Columnar Format -- Core Library"
keywords.workspace = true
categories.workspace = true
rust-version.workspace = true

[dependencies]
arrow-array.workspace = true
arrow-buffer.workspace = true
arrow-schema.workspace = true
async-trait.workspace = true
lance-arrow.workspace = true
byteorder.workspace = true
bytes.workspace = true
chrono.workspace = true
datafusion-common = { workspace = true, optional = true }
datafusion-sql = { workspace = true, optional = true }
deepsize.workspace = true
futures.workspace = true
lazy_static.workspace = true
mock_instant.workspace = true
moka.workspace = true
num_cpus = "1.0"
object_store = { workspace = true }
pin-project.workspace = true
prost.workspace = true
rand.workspace = true
roaring.workspace = true
serde_json.workspace = true
snafu.workspace = true
tokio.workspace = true
tokio-stream.workspace = true
tokio-util.workspace = true
tracing.workspace = true
url.workspace = true
log.workspace = true

# This is used to detect CPU features at runtime.
# See src/utils/cpu.rs
[target.'cfg(all(any(target_arch = "aarch64", target_arch = "loongarch64"), target_os = "linux"))'.dependencies]
libc = { version = "0.2" }

[dev-dependencies]
tempfile.workspace = true
lance-testing.workspace = true
proptest.workspace = true

[features]
datafusion = ["datafusion-common", "datafusion-sql"]

[lints]
workspace = true
