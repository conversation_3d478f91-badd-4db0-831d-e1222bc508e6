[package]
name = "lance-table"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true
readme = "README.md"
description = "Utilities for the Lance table format"
keywords.workspace = true
categories.workspace = true
rust-version.workspace = true

[dependencies]
lance-arrow.workspace = true
lance-core.workspace = true
lance-file.workspace = true
lance-io.workspace = true
arrow.workspace = true
arrow-array.workspace = true
arrow-buffer.workspace = true
arrow-ipc.workspace = true
arrow-schema.workspace = true
async-trait.workspace = true
aws-credential-types = { workspace = true, optional = true }
aws-sdk-dynamodb = { workspace = true, optional = true }
byteorder.workspace = true
bytes.workspace = true
chrono.workspace = true
deepsize.workspace = true
futures.workspace = true
lazy_static = { workspace = true, optional = true }
log.workspace = true
object_store.workspace = true
prost.workspace = true
prost-types.workspace = true
rand.workspace = true
rangemap.workspace = true
roaring.workspace = true
serde.workspace = true
serde_json.workspace = true
snafu.workspace = true
tokio.workspace = true
tracing.workspace = true
url.workspace = true
uuid.workspace = true

[dev-dependencies]
lance-datagen.workspace = true
arrow-schema.workspace = true
criterion.workspace = true
pretty_assertions.workspace = true
proptest.workspace = true
rstest.workspace = true
tempfile.workspace = true

[target.'cfg(target_os = "linux")'.dev-dependencies]
pprof = { workspace = true }

[build-dependencies]
prost-build.workspace = true
protobuf-src = { version = "2.1", optional = true }

[features]
dynamodb = ["aws-sdk-dynamodb", "lazy_static", "aws-credential-types", "lance-io/aws"]
protoc = ["dep:protobuf-src"]

[package.metadata.docs.rs]
# docs.rs uses an older version of Ubuntu that does not have the necessary protoc version
features = ["protoc"]

[[bench]]
name = "row_id_index"
harness = false

[lints]
workspace = true
