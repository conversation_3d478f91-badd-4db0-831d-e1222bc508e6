[package]
name = "lance-index"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true
readme = "README.md"
description = "Lance indices implementation"
keywords.workspace = true
categories.workspace = true
rust-version.workspace = true

[dependencies]
arrow.workspace = true
arrow-array.workspace = true
arrow-ord.workspace = true
arrow-schema.workspace = true
arrow-select.workspace = true
async-recursion.workspace = true
async-trait.workspace = true
bitvec.workspace = true
datafusion-common.workspace = true
datafusion-expr.workspace = true
datafusion-physical-expr.workspace = true
datafusion-sql.workspace = true
datafusion.workspace = true
deepsize.workspace = true
dirs.workspace = true
fst.workspace = true
futures.workspace = true
half.workspace = true
itertools.workspace = true
jieba-rs = { workspace = true, optional = true }
lance-arrow.workspace = true
lance-core.workspace = true
lance-datafusion.workspace = true
lance-encoding.workspace = true
lance-file.workspace = true
lance-io.workspace = true
lance-linalg.workspace = true
lance-table.workspace = true
lazy_static.workspace = true
log.workspace = true
moka.workspace = true
num-traits.workspace = true
object_store.workspace = true
prost.workspace = true
rand.workspace = true
roaring.workspace = true
rayon.workspace = true
serde_json.workspace = true
serde.workspace = true
snafu.workspace = true
tantivy.workspace = true
lindera = { workspace = true, optional = true }
lindera-tantivy = { workspace = true, optional = true }
tokio.workspace = true
tracing.workspace = true
tempfile.workspace = true
crossbeam-queue.workspace = true
bytes.workspace = true
uuid.workspace = true
async-channel = "2.3.1"
bitpacking = { version = "0.9.2", features = ["bitpacker4x"] }

[dev-dependencies]
approx.workspace = true
clap = { workspace = true, features = ["derive"] }
criterion.workspace = true
env_logger = "0.11.6"
lance-datagen.workspace = true
lance-testing.workspace = true
tempfile.workspace = true
test-log.workspace = true
datafusion-sql.workspace = true
random_word = { version = "0.4.3", features = ["en"] }

[features]
protoc = ["dep:protobuf-src"]
tokenizer-lindera = ["lindera", "lindera-tantivy", "tokenizer-common"]
tokenizer-jieba = ["jieba-rs", "tokenizer-common"]
tokenizer-common = []

[build-dependencies]
prost-build.workspace = true
protobuf-src = { version = "2.1", optional = true }

[target.'cfg(target_os = "linux")'.dev-dependencies]
pprof.workspace = true

[package.metadata.docs.rs]
# docs.rs uses an older version of Ubuntu that does not have the necessary protoc version
features = ["protoc"]

[[bench]]
name = "find_partitions"
harness = false

[[bench]]
name = "pq_dist_table"
harness = false

[[bench]]
name = "4bitpq_dist_table"
harness = false

[[bench]]
name = "pq_assignment"
harness = false

[[bench]]
name = "hnsw"
harness = false

[[bench]]
name = "sq"
harness = false

[[bench]]
name = "ngram"
harness = false

[[bench]]
name = "inverted"
harness = false

[lints]
workspace = true
