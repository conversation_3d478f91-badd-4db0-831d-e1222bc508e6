[package]
name = "lance"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true
description.workspace = true
readme = "README.md"
rust-version.workspace = true
keywords.workspace = true
categories.workspace = true

[package.metadata.docs.rs]
features = []
no-default-features = true

[dependencies]
lance-arrow = { workspace = true }
lance-core = { workspace = true }
lance-datafusion = { workspace = true }
lance-encoding = { workspace = true }
lance-file = { workspace = true }
lance-io = { workspace = true }
lance-linalg = { workspace = true }
lance-index = { workspace = true }
lance-table = { workspace = true }
arrow-arith = { workspace = true }
arrow-array = { workspace = true }
arrow-buffer = { workspace = true }
arrow-ipc = { workspace = true }
arrow-ord = { workspace = true }
arrow-row = { workspace = true }
arrow-schema = { workspace = true }
arrow-select = { workspace = true }
async-recursion.workspace = true
async-trait.workspace = true
byteorder.workspace = true
bytes.workspace = true
chrono.workspace = true
clap = { version = "4.1.1", features = ["derive"], optional = true }
# This is already used by datafusion
dashmap = "6"
deepsize.workspace = true
# matches arrow-rs use
half.workspace = true
itertools.workspace = true
object_store = { workspace = true }
aws-credential-types.workspace = true
pin-project.workspace = true
prost.workspace = true
prost-types.workspace = true
roaring.workspace = true
tokio.workspace = true
url.workspace = true
rand.workspace = true
futures.workspace = true
uuid.workspace = true
arrow.workspace = true
# TODO: use datafusion sub-modules to reduce build size?
datafusion.workspace = true
datafusion-functions.workspace = true
datafusion-physical-expr.workspace = true
datafusion-physical-plan.workspace = true
datafusion-expr.workspace = true
either.workspace = true
lapack = { version = "0.19.0", optional = true }
snafu = { workspace = true }
log = { workspace = true }
serde_json = { workspace = true }
serde = { workspace = true }
moka.workspace = true
permutation = { version = "0.4.0" }
tantivy.workspace = true
tfrecord = { version = "0.15.0", optional = true, features = ["async"] }
prost_old = { version = "0.12.6", package = "prost", optional = true }
aws-sdk-dynamodb = { workspace = true, optional = true }
tempfile.workspace = true
tracing.workspace = true
lazy_static = { workspace = true }
humantime = { workspace = true }
async_cell = "0.2.2"

[target.'cfg(target_os = "linux")'.dev-dependencies]
pprof.workspace = true
# Need this so we can prevent dynamic linking in binaries (see cli feature)
lzma-sys = { version = "0.1" }

[dev-dependencies]
lance-test-macros = { workspace = true }
lance-datagen = { workspace = true }
pretty_assertions = { workspace = true }
clap = { workspace = true, features = ["derive"] }
criterion = { workspace = true }
approx.workspace = true
dirs = "5.0.0"
all_asserts = "2.3.1"
mock_instant.workspace = true
lance-testing = { workspace = true }
tracing-subscriber = { version = "0.3.17", features = ["env-filter"] }
env_logger = "0.11.7"
test-log.workspace = true
tracing-chrome = "0.7.1"
rstest = { workspace = true }
random_word = { version = "0.4.3", features = ["en"] }
# For S3 / DynamoDB tests
aws-config = { workspace = true }
aws-sdk-s3 = { workspace = true }


[features]
default = ["aws", "azure", "gcp"]
fp16kernels = ["lance-linalg/fp16kernels"]
# Prevent dynamic linking of lzma, which comes from datafusion
cli = ["clap", "lzma-sys/static"]
tensorflow = ["tfrecord", "prost_old"]
dynamodb = ["lance-table/dynamodb", "aws-sdk-dynamodb"]
dynamodb_tests = ["dynamodb"]
substrait = ["lance-datafusion/substrait"]
protoc = [
    "lance-encoding/protoc",
    "lance-file/protoc",
    "lance-index/protoc",
    "lance-table/protoc",
]
aws = ["lance-io/aws"]
gcp = ["lance-io/gcp"]
azure = ["lance-io/azure"]

[[bin]]
name = "lq"
required-features = ["cli"]

[[bench]]
name = "scalar_index"
harness = false

[[bench]]
name = "scan"
harness = false

[[bench]]
name = "vector_index"
harness = false

[[bench]]
name = "ivf_pq"
harness = false

[[bench]]
name = "take"
harness = false

[lints]
workspace = true
