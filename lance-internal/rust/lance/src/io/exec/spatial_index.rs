// SPDX-License-Identifier: Apache-2.0
// SPDX-FileCopyrightText: Copyright The Lance Authors

use std::sync::Arc;

use arrow_array::{RecordBatch, UInt64Array};
use arrow_schema::{Schema, SchemaRef};
use datafusion::{
    common::Statistics,
    physical_plan::{
        execution_plan::{Boundedness, EmissionType},
        metrics::{ExecutionPlanMetricsSet, MetricsSet},
        stream::RecordBatchStreamAdapter,
        DisplayAs, DisplayFormatType, ExecutionPlan, PlanProperties,
    },
};
use datafusion_physical_expr::{EquivalenceProperties, Partitioning};
use lance_core::ROW_ID_FIELD;
use lance_table::format::Fragment;

use crate::Dataset;

lazy_static::lazy_static! {
    pub static ref SPATIAL_INDEX_SCHEMA: SchemaRef = Arc::new(Schema::new(vec![ROW_ID_FIELD.clone()]));
}

/// Represents a spatial bounding box query
#[derive(Debug, Clone)]
pub struct SpatialBoundingBox {
    pub xmin: f64,
    pub ymin: f64,
    pub xmax: f64,
    pub ymax: f64,
}

/// An execution node that performs spatial filtering using GeoPage indices
#[derive(Debug)]
pub struct SpatialIndexExec {
    dataset: Arc<Dataset>,
    fragments: Arc<Vec<Fragment>>,
    bbox: SpatialBoundingBox,
    column_name: String,
    properties: PlanProperties,
    metrics: ExecutionPlanMetricsSet,
}

impl SpatialIndexExec {
    pub fn new(
        dataset: Arc<Dataset>,
        fragments: Arc<Vec<Fragment>>,
        column_name: String,
        bbox: SpatialBoundingBox,
    ) -> Self {
        let properties = PlanProperties::new(
            EquivalenceProperties::new(SPATIAL_INDEX_SCHEMA.clone()),
            Partitioning::UnknownPartitioning(1),
            EmissionType::Incremental,
            Boundedness::Bounded,
        );

        Self {
            dataset,
            fragments,
            column_name,
            bbox,
            properties,
            metrics: ExecutionPlanMetricsSet::new(),
        }
    }
}

impl DisplayAs for SpatialIndexExec {
    fn fmt_as(&self, t: DisplayFormatType, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        match t {
            DisplayFormatType::Default | DisplayFormatType::Verbose => {
                write!(
                    f,
                    "SpatialIndex: column={}, bbox=[{},{},{},{}]",
                    self.column_name, self.bbox.xmin, self.bbox.ymin, 
                    self.bbox.xmax, self.bbox.ymax
                )
            }
            DisplayFormatType::TreeRender => write!(f, "SpatialIndex"),
        }
    }
}

impl ExecutionPlan for SpatialIndexExec {
    fn name(&self) -> &str {
        "SpatialIndexExec"
    }

    fn as_any(&self) -> &dyn std::any::Any {
        self
    }

    fn schema(&self) -> SchemaRef {
        SPATIAL_INDEX_SCHEMA.clone()
    }

    fn children(&self) -> Vec<&Arc<dyn ExecutionPlan>> {
        vec![]
    }

    fn with_new_children(
        self: Arc<Self>,
        children: Vec<Arc<dyn ExecutionPlan>>,
    ) -> datafusion::error::Result<Arc<dyn ExecutionPlan>> {
        if !children.is_empty() {
            return Err(datafusion::error::DataFusionError::Internal(
                "SpatialIndexExec does not accept children".to_string(),
            ));
        }
        Ok(self)
    }

    fn execute(
        &self,
        _partition: usize,
        _context: Arc<datafusion::execution::context::TaskContext>,
    ) -> datafusion::error::Result<datafusion::physical_plan::SendableRecordBatchStream> {
        let bbox = self.bbox.clone();
        let column_name = self.column_name.clone();
        let fragments = self.fragments.clone();
        
        // Create the spatial filter expression
        let filter_bytes = format!(
            "bbox:{},{},{},{}",
            bbox.xmin, bbox.ymin, bbox.xmax, bbox.ymax
        );
        
        let stream = futures::stream::once(async move {
            // For now, return all row IDs - the actual filtering happens in GeoPageFieldScheduler
            // In a full implementation, we would query the spatial index here
            let mut row_ids = Vec::new();
            let mut row_id_base = 0u64;
            
            for fragment in fragments.iter() {
                let num_rows = fragment.num_rows().unwrap_or(0) as u64;
                row_ids.extend(row_id_base..row_id_base + num_rows);
                row_id_base += num_rows;
            }
            
            Ok(RecordBatch::try_new(
                SPATIAL_INDEX_SCHEMA.clone(),
                vec![Arc::new(UInt64Array::from(row_ids))],
            )?)
        });
        
        Ok(Box::pin(RecordBatchStreamAdapter::new(
            SPATIAL_INDEX_SCHEMA.clone(),
            stream,
        )))
    }

    fn statistics(&self) -> datafusion::error::Result<Statistics> {
        Ok(Statistics::new_unknown(self.schema().as_ref()))
    }

    fn metrics(&self) -> Option<MetricsSet> {
        Some(self.metrics.clone_inner())
    }

    fn properties(&self) -> &PlanProperties {
        &self.properties
    }
}
