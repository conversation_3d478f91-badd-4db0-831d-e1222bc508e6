// SPDX-License-Identifier: Apache-2.0
// SPDX-FileCopyrightText: Copyright The Lance Authors

//! WKB (Well-Known Binary) parsing utilities for Lance spatial data support
//!
//! This module provides utilities for parsing WKB geometry data and extracting
//! representative points for SSD-level spatial sorting in Lance's storage engine.
//!
//! Focus: Storage-level optimization, not query engine features.
//! Goal: Extract consistent spatial keys for disk-level data organization.

use lance_core::{Error, Result};
use wkb::reader::{Wkb, GeometryType};
use snafu::location;
use geo_traits::{GeometryTrait, PointTrait, LineStringTrait, PolygonTrait, CoordTrait};

/// A spatial point for SSD-level spatial sorting
///
/// This represents a single coordinate used for generating spatial keys
/// for disk-level data organization. Not meant for geometric calculations.
#[derive(Debu<PERSON>, <PERSON><PERSON>, Co<PERSON>, PartialEq)]
pub struct SpatialPoint {
    pub lon: f64,
    pub lat: f64,
}

impl SpatialPoint {
    pub fn new(lon: f64, lat: f64) -> Self {
        Self { lon, lat }
    }

    /// Check if coordinates are within reasonable ranges for spatial sorting
    pub fn is_valid(&self) -> bool {
        self.lon.is_finite() && self.lat.is_finite() &&
        self.lon >= -180.0 && self.lon <= 180.0 &&
        self.lat >= -90.0 && self.lat <= 90.0
    }
}

/// Simple WKB parser for essential geometry types only
///
/// Focus: Extract representative points for SSD-level spatial sorting.
/// Supports: Point, LineString, Polygon (essential types only).
pub struct SimpleWkbParser;

impl SimpleWkbParser {
    /// Extract a representative point from WKB geometry data for spatial sorting
    ///
    /// This extracts a single coordinate that can be used to generate spatial keys
    /// for disk-level data organization. Strategies:
    /// - Point: Direct coordinates
    /// - LineString: First coordinate (simple and fast)
    /// - Polygon: Simple centroid of exterior ring
    ///
    /// Note: This is NOT a geometric centroid calculation - just a consistent
    /// point extraction for spatial sorting keys.
    pub fn extract_representative_point(wkb_bytes: &[u8]) -> Result<SpatialPoint> {
        if wkb_bytes.is_empty() {
            return Err(Error::InvalidInput {
                source: "Empty WKB data".into(),
                location: location!(),
            });
        }

        // Check minimum WKB size (1 byte endian + 4 bytes type = 5 bytes minimum)
        if wkb_bytes.len() < 5 {
            return Err(Error::InvalidInput {
                source: "WKB data too short".into(),
                location: location!(),
            });
        }

        // Use catch_unwind to handle potential panics from the WKB crate
        let wkb_geom = std::panic::catch_unwind(|| {
            Wkb::try_new(wkb_bytes)
        }).map_err(|_| Error::InvalidInput {
            source: "WKB parsing panicked (likely due to malformed data)".into(),
            location: location!(),
        })?.map_err(|e| Error::InvalidInput {
            source: format!("Failed to parse WKB: {}", e).into(),
            location: location!(),
        })?;

        let geom_type = wkb_geom.geometry_type();

        match geom_type {
            GeometryType::Point => Self::extract_point_coords(&wkb_geom),
            GeometryType::LineString => Self::extract_linestring_first_point(&wkb_geom),
            GeometryType::Polygon => Self::extract_polygon_simple_centroid(&wkb_geom),
            _ => Err(Error::InvalidInput {
                source: format!("Unsupported geometry type for spatial sorting: {:?}. Only Point, LineString, and Polygon are supported.", geom_type).into(),
                location: location!(),
            }),
        }
    }

    /// Extract coordinates directly from WKB Point geometry
    fn extract_point_coords(wkb_geom: &Wkb) -> Result<SpatialPoint> {
        use geo_traits::GeometryType as GeoGeometryType;

        match wkb_geom.as_type() {
            GeoGeometryType::Point(point) => {
                let coord = point.coord().unwrap();
                let x = coord.x();
                let y = coord.y();

                let spatial_point = SpatialPoint::new(x, y);
                if !spatial_point.is_valid() {
                    return Err(Error::InvalidInput {
                        source: format!("Invalid Point coordinates: ({}, {})", x, y).into(),
                        location: location!(),
                    });
                }

                Ok(spatial_point)
            }
            _ => Err(Error::InvalidInput {
                source: "Expected Point geometry".into(),
                location: location!(),
            }),
        }
    }

    /// Extract first coordinate from LineString for spatial sorting
    fn extract_linestring_first_point(wkb_geom: &Wkb) -> Result<SpatialPoint> {
        use geo_traits::GeometryType as GeoGeometryType;

        match wkb_geom.as_type() {
            GeoGeometryType::LineString(linestring) => {
                if linestring.num_coords() == 0 {
                    return Err(Error::InvalidInput {
                        source: "Empty LineString".into(),
                        location: location!(),
                    });
                }

                // Get first coordinate
                let first_coord = linestring.coord(0).unwrap();
                let x = first_coord.x();
                let y = first_coord.y();

                let spatial_point = SpatialPoint::new(x, y);
                if !spatial_point.is_valid() {
                    return Err(Error::InvalidInput {
                        source: format!("Invalid LineString first point coordinates: ({}, {})", x, y).into(),
                        location: location!(),
                    });
                }

                Ok(spatial_point)
            }
            _ => Err(Error::InvalidInput {
                source: "Expected LineString geometry".into(),
                location: location!(),
            }),
        }
    }

    /// Extract simple centroid from Polygon exterior ring for spatial sorting
    fn extract_polygon_simple_centroid(wkb_geom: &Wkb) -> Result<SpatialPoint> {
        use geo_traits::GeometryType as GeoGeometryType;

        match wkb_geom.as_type() {
            GeoGeometryType::Polygon(polygon) => {
                let exterior = polygon.exterior().unwrap();
                let num_coords = exterior.num_coords();

                if num_coords < 4 {
                    return Err(Error::InvalidInput {
                        source: "Polygon exterior ring must have at least 4 points".into(),
                        location: location!(),
                    });
                }

                // Calculate simple centroid from exterior ring points
                let mut sum_x = 0.0;
                let mut sum_y = 0.0;
                let mut valid_points = 0;

                // Exclude the last point since it's a duplicate of the first point (ring closure)
                let coords_to_process = if num_coords > 0 { num_coords - 1 } else { 0 };

                for i in 0..coords_to_process {
                    let coord = exterior.coord(i).unwrap();
                    let x = coord.x();
                    let y = coord.y();

                    // Only include valid coordinates in centroid calculation
                    let point = SpatialPoint::new(x, y);
                    if point.is_valid() {
                        sum_x += x;
                        sum_y += y;
                        valid_points += 1;
                    }
                }

                if valid_points == 0 {
                    return Err(Error::InvalidInput {
                        source: "No valid coordinates found in polygon exterior ring".into(),
                        location: location!(),
                    });
                }

                let centroid_x = sum_x / valid_points as f64;
                let centroid_y = sum_y / valid_points as f64;

                Ok(SpatialPoint::new(centroid_x, centroid_y))
            }
            _ => Err(Error::InvalidInput {
                source: "Expected Polygon geometry".into(),
                location: location!(),
            }),
        }
    }



    /// Validate that binary data looks like supported WKB (essential types only)
    ///
    /// This performs a quick validation to check if the data could be valid WKB
    /// for our supported geometry types without fully parsing it.
    /// Only supports: Point(1), LineString(2), Polygon(3)
    pub fn is_likely_wkb(data: &[u8]) -> bool {
        if data.len() < 9 {
            return false; // WKB must be at least 9 bytes (1 byte order + 4 bytes type + 4 bytes for minimal geometry)
        }

        // Check byte order (first byte should be 0 or 1)
        let byte_order = data[0];
        if byte_order != 0 && byte_order != 1 {
            return false;
        }

        // Try to parse the geometry type (next 4 bytes)
        let geometry_type = if byte_order == 1 {
            // Little endian
            u32::from_le_bytes([data[1], data[2], data[3], data[4]])
        } else {
            // Big endian
            u32::from_be_bytes([data[1], data[2], data[3], data[4]])
        };

        // Check if geometry type is in our supported range (1-3 for essential types)
        let base_type = geometry_type & 0x1FFFFFFF; // Remove potential Z/M/SRID flags
        base_type >= 1 && base_type <= 3 // Only Point, LineString, Polygon
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_is_likely_wkb_essential_types() {
        // Test empty data
        assert!(!SimpleWkbParser::is_likely_wkb(&[]));

        // Test too short data
        assert!(!SimpleWkbParser::is_likely_wkb(&[1, 2, 3]));

        // Test invalid byte order
        assert!(!SimpleWkbParser::is_likely_wkb(&[2, 1, 0, 0, 0, 0, 0, 0, 0]));

        // Test valid-looking WKB header (little endian, Point type = 1)
        assert!(SimpleWkbParser::is_likely_wkb(&[1, 1, 0, 0, 0, 0, 0, 0, 0]));

        // Test valid-looking WKB header (big endian, Polygon type = 3)
        assert!(SimpleWkbParser::is_likely_wkb(&[0, 0, 0, 0, 3, 0, 0, 0, 0]));

        // Test unsupported geometry type (MultiPoint = 4) - should be rejected
        assert!(!SimpleWkbParser::is_likely_wkb(&[1, 4, 0, 0, 0, 0, 0, 0, 0]));

        // Test LineString type = 2 (should be supported)
        assert!(SimpleWkbParser::is_likely_wkb(&[1, 2, 0, 0, 0, 0, 0, 0, 0]));
    }

    #[test]
    fn test_spatial_point_validation() {
        // Test valid coordinates
        let point = SpatialPoint::new(-74.0, 40.7);
        assert!(point.is_valid());

        // Test invalid longitude
        let point = SpatialPoint::new(-200.0, 40.7);
        assert!(!point.is_valid());

        // Test invalid latitude
        let point = SpatialPoint::new(-74.0, 100.0);
        assert!(!point.is_valid());

        // Test infinite coordinates
        let point = SpatialPoint::new(f64::INFINITY, 40.7);
        assert!(!point.is_valid());
    }

    #[test]
    fn test_wkb_point_extraction() {
        // Create WKB for POINT(1.0 2.0) in little endian format
        let mut wkb_point = vec![
            0x01, // Little endian
            0x01, 0x00, 0x00, 0x00, // Point type (1)
        ];
        // Add x coordinate (1.0 as f64 little endian)
        wkb_point.extend_from_slice(&1.0_f64.to_le_bytes());
        // Add y coordinate (2.0 as f64 little endian)
        wkb_point.extend_from_slice(&2.0_f64.to_le_bytes());

        let result = SimpleWkbParser::extract_representative_point(&wkb_point);
        assert!(result.is_ok(), "Failed to parse WKB Point: {:?}", result.err());

        let point = result.unwrap();
        assert_eq!(point.lon, 1.0);
        assert_eq!(point.lat, 2.0);
    }

    #[test]
    fn test_wkb_linestring_extraction() {
        // Create WKB for LINESTRING(0 0, 1 1, 2 2) in little endian format
        let mut wkb_linestring = vec![
            0x01, // Little endian
            0x02, 0x00, 0x00, 0x00, // LineString type (2)
            0x03, 0x00, 0x00, 0x00, // Number of points (3)
        ];
        // Add first point (0.0, 0.0)
        wkb_linestring.extend_from_slice(&0.0_f64.to_le_bytes());
        wkb_linestring.extend_from_slice(&0.0_f64.to_le_bytes());
        // Add second point (1.0, 1.0)
        wkb_linestring.extend_from_slice(&1.0_f64.to_le_bytes());
        wkb_linestring.extend_from_slice(&1.0_f64.to_le_bytes());
        // Add third point (2.0, 2.0)
        wkb_linestring.extend_from_slice(&2.0_f64.to_le_bytes());
        wkb_linestring.extend_from_slice(&2.0_f64.to_le_bytes());

        let result = SimpleWkbParser::extract_representative_point(&wkb_linestring);
        assert!(result.is_ok(), "Failed to parse WKB LineString: {:?}", result.err());

        let point = result.unwrap();
        // Should return first point (0.0, 0.0)
        assert_eq!(point.lon, 0.0);
        assert_eq!(point.lat, 0.0);
    }

    #[test]
    fn test_wkb_polygon_extraction() {
        // Create WKB for simple square polygon in little endian format
        let mut wkb_polygon = vec![
            0x01, // Little endian
            0x03, 0x00, 0x00, 0x00, // Polygon type (3)
            0x01, 0x00, 0x00, 0x00, // Number of rings (1)
            0x05, 0x00, 0x00, 0x00, // Number of points in exterior ring (5, closed)
        ];

        // Add square coordinates: (0,0), (1,0), (1,1), (0,1), (0,0)
        let coords: [(f64, f64); 5] = [(0.0, 0.0), (1.0, 0.0), (1.0, 1.0), (0.0, 1.0), (0.0, 0.0)];
        for (x, y) in coords {
            wkb_polygon.extend_from_slice(&x.to_le_bytes());
            wkb_polygon.extend_from_slice(&y.to_le_bytes());
        }

        let result = SimpleWkbParser::extract_representative_point(&wkb_polygon);
        assert!(result.is_ok(), "Failed to parse WKB Polygon: {:?}", result.err());

        let point = result.unwrap();

        // Should return centroid (0.5, 0.5)
        assert!((point.lon - 0.5).abs() < 0.001, "Expected lon=0.5, got {}", point.lon);
        assert!((point.lat - 0.5).abs() < 0.001, "Expected lat=0.5, got {}", point.lat);
    }

    #[test]
    fn test_wkb_big_endian_point() {
        // Create WKB for POINT(1.0 2.0) in big endian format
        let mut wkb_point = vec![
            0x00, // Big endian
            0x00, 0x00, 0x00, 0x01, // Point type (1)
        ];
        // Add x coordinate (1.0 as f64 big endian)
        wkb_point.extend_from_slice(&1.0_f64.to_be_bytes());
        // Add y coordinate (2.0 as f64 big endian)
        wkb_point.extend_from_slice(&2.0_f64.to_be_bytes());

        let result = SimpleWkbParser::extract_representative_point(&wkb_point);
        assert!(result.is_ok(), "Failed to parse big endian WKB Point: {:?}", result.err());

        let point = result.unwrap();
        assert_eq!(point.lon, 1.0);
        assert_eq!(point.lat, 2.0);
    }

    #[test]
    fn test_wkb_invalid_coordinates() {
        // Create WKB Point with invalid coordinates (outside WGS84 bounds)
        let mut wkb_point = vec![
            0x01, // Little endian
            0x01, 0x00, 0x00, 0x00, // Point type (1)
        ];
        // Add invalid x coordinate (200.0, outside -180 to 180 range)
        wkb_point.extend_from_slice(&200.0_f64.to_le_bytes());
        // Add valid y coordinate
        wkb_point.extend_from_slice(&40.0_f64.to_le_bytes());

        let result = SimpleWkbParser::extract_representative_point(&wkb_point);
        assert!(result.is_err(), "Should reject invalid coordinates");
    }

    #[test]
    fn test_wkb_empty_data() {
        let result = SimpleWkbParser::extract_representative_point(&[]);
        assert!(result.is_err(), "Should reject empty data");
    }

    #[test]
    fn test_wkb_too_short() {
        let short_data = vec![0x01, 0x01]; // Only 2 bytes
        let result = SimpleWkbParser::extract_representative_point(&short_data);
        assert!(result.is_err(), "Should reject data that's too short");
    }
}
