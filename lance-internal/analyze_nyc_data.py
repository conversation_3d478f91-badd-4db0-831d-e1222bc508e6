#!/usr/bin/env python3
"""
Deep analysis of NYC taxi data to understand GeoPage performance characteristics
"""

import pyarrow.parquet as pq
import pyarrow as pa
import numpy as np
import os
from collections import defaultdict
import math

def analyze_nyc_taxi_data():
    """Comprehensive analysis of NYC taxi data spatial distribution"""
    
    print("🔍 DEEP ANALYSIS: NYC Taxi Data Spatial Characteristics")
    print("=" * 60)
    
    # Load the data
    parquet_path = os.path.expanduser("~/Work/terrafloww/geo-research/uber_data/nyc-taxi/2009/01/data.parquet")
    print(f"📂 Loading: {parquet_path}")
    
    table = pq.read_table(parquet_path)
    print(f"📊 Total rows: {len(table):,}")
    
    # Extract coordinates
    pickup_lons = table['pickup_longitude'].to_numpy()
    pickup_lats = table['pickup_latitude'].to_numpy()
    dropoff_lons = table['dropoff_longitude'].to_numpy()
    dropoff_lats = table['dropoff_latitude'].to_numpy()
    
    # Remove invalid coordinates (zeros, nulls, extreme outliers)
    valid_pickup = (pickup_lons != 0) & (pickup_lats != 0) & \
                   (pickup_lons >= -180) & (pickup_lons <= 180) & \
                   (pickup_lats >= -90) & (pickup_lats <= 90)
    
    valid_dropoff = (dropoff_lons != 0) & (dropoff_lats != 0) & \
                    (dropoff_lons >= -180) & (dropoff_lons <= 180) & \
                    (dropoff_lats >= -90) & (dropoff_lats <= 90)
    
    print(f"📍 Valid pickup coordinates: {valid_pickup.sum():,} ({100*valid_pickup.sum()/len(table):.1f}%)")
    print(f"📍 Valid dropoff coordinates: {valid_dropoff.sum():,} ({100*valid_dropoff.sum()/len(table):.1f}%)")
    
    # Focus on pickup coordinates for analysis
    valid_lons = pickup_lons[valid_pickup]
    valid_lats = pickup_lats[valid_pickup]
    
    print(f"\n📊 SPATIAL BOUNDS ANALYSIS:")
    print(f"🌍 Longitude range: {valid_lons.min():.6f} to {valid_lons.max():.6f}")
    print(f"🌍 Latitude range: {valid_lats.min():.6f} to {valid_lats.max():.6f}")
    print(f"📏 Longitude span: {valid_lons.max() - valid_lons.min():.6f} degrees")
    print(f"📏 Latitude span: {valid_lats.max() - valid_lats.min():.6f} degrees")
    
    # Calculate NYC-specific bounds
    nyc_bounds = {
        'lon_min': valid_lons.min(),
        'lon_max': valid_lons.max(), 
        'lat_min': valid_lats.min(),
        'lat_max': valid_lats.max()
    }
    
    # Analyze spatial density and clustering
    print(f"\n🗺️  SPATIAL DENSITY ANALYSIS:")
    
    # Create a grid to analyze density
    grid_size = 50
    lon_bins = np.linspace(nyc_bounds['lon_min'], nyc_bounds['lon_max'], grid_size)
    lat_bins = np.linspace(nyc_bounds['lat_min'], nyc_bounds['lat_max'], grid_size)
    
    hist, _, _ = np.histogram2d(valid_lons, valid_lats, bins=[lon_bins, lat_bins])
    
    non_empty_cells = (hist > 0).sum()
    total_cells = grid_size * grid_size
    density_ratio = non_empty_cells / total_cells
    
    print(f"📊 Grid analysis ({grid_size}x{grid_size} cells):")
    print(f"   Non-empty cells: {non_empty_cells}/{total_cells} ({100*density_ratio:.1f}%)")
    print(f"   Max density per cell: {hist.max():,} trips")
    print(f"   Mean density (non-empty): {hist[hist > 0].mean():.0f} trips")
    print(f"   Std density (non-empty): {hist[hist > 0].std():.0f} trips")
    
    # Analyze quadkey distribution at different zoom levels
    print(f"\n🔢 QUADKEY ANALYSIS:")
    
    for zoom in [10, 11, 12, 13, 14]:
        quadkeys = []
        for lon, lat in zip(valid_lons[:10000], valid_lats[:10000]):  # Sample for speed
            quadkey = lat_lon_to_quadkey(lat, lon, zoom)
            quadkeys.append(quadkey)
        
        unique_quadkeys = len(set(quadkeys))
        total_possible = 4 ** zoom
        coverage = unique_quadkeys / total_possible
        
        print(f"   Zoom {zoom}: {unique_quadkeys:,} unique quadkeys ({100*coverage:.3f}% coverage)")
        print(f"            Avg points per quadkey: {len(quadkeys)/unique_quadkeys:.1f}")
    
    # Analyze Manhattan vs outer boroughs
    print(f"\n🏙️  GEOGRAPHIC DISTRIBUTION:")
    
    # Manhattan approximate bounds
    manhattan_bounds = {
        'lon_min': -74.02,
        'lon_max': -73.93,
        'lat_min': 40.70,
        'lat_max': 40.80
    }
    
    manhattan_mask = (
        (valid_lons >= manhattan_bounds['lon_min']) & 
        (valid_lons <= manhattan_bounds['lon_max']) &
        (valid_lats >= manhattan_bounds['lat_min']) & 
        (valid_lats <= manhattan_bounds['lat_max'])
    )
    
    manhattan_trips = manhattan_mask.sum()
    manhattan_pct = 100 * manhattan_trips / len(valid_lons)
    
    print(f"🏙️  Manhattan trips: {manhattan_trips:,} ({manhattan_pct:.1f}%)")
    print(f"🌆 Outer borough trips: {len(valid_lons) - manhattan_trips:,} ({100-manhattan_pct:.1f}%)")
    
    # Analyze why spatial filtering might not be effective
    print(f"\n❓ WHY LOW SPATIAL FILTERING EFFECTIVENESS?")
    
    # Check data clustering
    lon_std = valid_lons.std()
    lat_std = valid_lats.std()
    
    print(f"📊 Coordinate standard deviation:")
    print(f"   Longitude std: {lon_std:.6f} degrees")
    print(f"   Latitude std: {lat_std:.6f} degrees")
    
    # Convert to approximate meters (at NYC latitude ~40.7°)
    lat_40_7 = 40.7
    meters_per_deg_lon = 111320 * math.cos(math.radians(lat_40_7))
    meters_per_deg_lat = 111320
    
    lon_std_meters = lon_std * meters_per_deg_lon
    lat_std_meters = lat_std * meters_per_deg_lat
    
    print(f"   Longitude std: ~{lon_std_meters:.0f} meters")
    print(f"   Latitude std: ~{lat_std_meters:.0f} meters")
    
    # Analyze our test query effectiveness
    test_query_bounds = {
        'lon_min': -74.02,
        'lon_max': -73.93, 
        'lat_min': 40.70,
        'lat_max': 40.80
    }
    
    query_area_deg2 = (test_query_bounds['lon_max'] - test_query_bounds['lon_min']) * \
                      (test_query_bounds['lat_max'] - test_query_bounds['lat_min'])
    
    total_area_deg2 = (nyc_bounds['lon_max'] - nyc_bounds['lon_min']) * \
                      (nyc_bounds['lat_max'] - nyc_bounds['lat_min'])
    
    area_ratio = query_area_deg2 / total_area_deg2
    
    print(f"\n🎯 TEST QUERY ANALYSIS:")
    print(f"📏 Query area: {query_area_deg2:.6f} deg² ({100*area_ratio:.1f}% of total)")
    print(f"📊 Expected data reduction: ~{100*(1-area_ratio):.1f}%")
    print(f"📊 Actual data reduction: 8.5%")
    print(f"❗ Gap: Query area too large! Most trips are in Manhattan anyway.")
    
    # Suggest better test queries
    print(f"\n💡 SUGGESTED IMPROVEMENTS:")
    print(f"1. 🎯 Use smaller, more selective spatial queries")
    print(f"2. 🔍 Test with outer borough vs Manhattan queries") 
    print(f"3. 📊 Use zoom level 13-14 for finer spatial resolution")
    print(f"4. 🗺️  Test with specific neighborhoods (e.g., just Financial District)")
    
    # Analyze Lance page structure implications
    print(f"\n📄 LANCE PAGE STRUCTURE IMPLICATIONS:")
    
    # Estimate rows per page (Lance default ~1024 rows per group)
    rows_per_page = 1024
    total_pages = math.ceil(len(table) / rows_per_page)
    
    print(f"📊 Estimated Lance pages: {total_pages:,}")
    print(f"📊 Rows per page: ~{rows_per_page}")
    
    # If data is temporally sorted, spatial locality might be poor
    print(f"📅 Data likely sorted by pickup_at (temporal), not spatial")
    print(f"❗ This means each page contains trips from across NYC")
    print(f"❗ Spatial filtering can't skip many pages")
    
    return {
        'total_rows': len(table),
        'valid_coords': len(valid_lons),
        'bounds': nyc_bounds,
        'manhattan_pct': manhattan_pct,
        'area_ratio': area_ratio,
        'total_pages': total_pages
    }

def lat_lon_to_quadkey(lat, lon, zoom):
    """Convert lat/lon to quadkey (simplified version)"""
    lat_rad = math.radians(lat)
    n = 2.0 ** zoom
    x = int((lon + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    
    quadkey = 0
    for i in range(zoom):
        bit = zoom - i - 1
        digit = ((y >> bit) & 1) << 1 | ((x >> bit) & 1)
        quadkey = (quadkey << 2) | digit
    
    return quadkey

if __name__ == "__main__":
    results = analyze_nyc_taxi_data()
    
    print(f"\n🎯 KEY INSIGHTS:")
    print(f"1. 📊 Data is likely temporally sorted, not spatially clustered")
    print(f"2. 🗺️  Manhattan contains {results['manhattan_pct']:.1f}% of trips")
    print(f"3. 🎯 Our test query was too broad ({100*results['area_ratio']:.1f}% of area)")
    print(f"4. 📄 {results['total_pages']:,} Lance pages with mixed spatial data per page")
    print(f"5. ❗ Need smaller, more selective spatial queries to see benefits")
