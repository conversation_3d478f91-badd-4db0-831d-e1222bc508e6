# Changelog

All notable changes to <PERSON> will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added - GeoPage Spatial Codec 🗺️

### Fixed - Robust Error Handling & No Hardcoded Values 🔧
- **Removed all hardcoded coordinates** - No more San Francisco fallback coordinates
- **Proper error propagation** - Follow Lance patterns for Result<T> error handling
- **Enhanced spatial detection** - Validate actual data content before applying GeoPage
- **Graceful fallbacks** - Let <PERSON>'s encoder selection handle non-spatial data
- **Improved test robustness** - Use generic test coordinates instead of hardcoded values

#### Major Features
- **GeoPage spatial encoding** for efficient geospatial data storage and querying
- **Automatic spatial column detection** based on column name patterns (lat/lon, geometry, etc.)
- **Quadtree spatial indexing** with Z-order curve data sorting for optimal spatial locality
- **Multiple spatial filter formats** supporting SQL, PostGIS, and bbox syntax
- **Efficient spatial filtering** for range queries with quadtree optimization

#### Implementation Details
- **Core Implementation**: `rust/lance-encoding/src/encodings/geopage.rs`
- **Protobuf Schema**: `protos/geopage.proto` with spatial metadata structures
- **Encoder Integration**: Automatic GeoPage selection in `rust/lance-encoding/src/encoder.rs`
- **Spatial Utilities**: Microsoft Bing Maps quadkey generation and spatial operations
- **Field Scheduler**: `GeoPageFieldScheduler` for spatial query optimization

#### Spatial Features
- **Coordinate Processing**: Real lat/lon extraction from Float64 arrays
- **Geometry Support**: WKT POINT parsing for variable-width geometry data
- **Bounding Box Calculation**: Automatic spatial bounds computation
- **Spatial Validation**: Coordinate range checking (-180/180, -90/90)
- **Morton Encoding**: Z-order curve spatial sorting for cache-friendly access

#### Query Optimization
- **Spatial Filtering**: Integrated into Lance's scan pipeline
- **Range Pruning**: Page-level spatial filtering using quadtree index
- **Binary Search**: O(log n) spatial lookups on sorted quadkey index
- **Multiple Filter Formats**:
  - `bbox:xmin,ymin,xmax,ymax`
  - `longitude >= -122.5 AND latitude >= 37.7`
  - `ST_Intersects(geom, ST_MakeEnvelope(...))`

#### Performance Characteristics
- **Spatial Filtering**: Efficient data reduction for spatial range queries
- **Query Speed**: Fast spatial lookups using quadtree indexing
- **Scalability**: O(log n) spatial index lookups
- **Memory Efficiency**: 4KiB-aligned pages for optimal I/O patterns

#### Testing & Validation
- **Comprehensive Test Suite**: 9 test cases covering all GeoPage functionality
- **Performance Simulation**: Spatial filtering benchmarks with real-world scenarios
- **Integration Tests**: End-to-end testing with Lance's query pipeline
- **Encoder Selection Tests**: Automatic spatial column detection validation

#### Documentation
- **Architecture Guide**: `docs/GEOPAGE_ARCHITECTURE.md`
- **Implementation Guide**: `docs/GEOPAGE_IMPLEMENTATION_GUIDE.md`
- **Technical Deep Dive**: `docs/GEOPAGE_TECHNICAL_DEEP_DIVE.md`
- **API Documentation**: Comprehensive inline documentation and examples

#### Files Added/Modified
```
rust/lance-encoding/src/encodings/geopage.rs    [NEW] - Core GeoPage implementation
rust/lance-encoding/src/encoder.rs              [MOD] - Automatic spatial detection
rust/lance-encoding/src/encodings/mod.rs        [MOD] - Module registration
protos/geopage.proto                            [NEW] - Spatial metadata schema
docs/GEOPAGE_*.md                               [NEW] - Comprehensive documentation
README.md                                       [MOD] - Updated with GeoPage examples
rust/README.md                                  [MOD] - Rust workspace documentation
```

#### Backward Compatibility
- **Fully backward compatible** with existing Lance datasets
- **Optional encoding** - only applied to detected spatial columns
- **Graceful fallback** to standard encodings for non-spatial data
- **Zero breaking changes** to existing APIs

#### Future Enhancements
- **WKB geometry support** for binary geometry formats
- **Multi-polygon indexing** for complex geometries
- **Spatial joins** optimization using dual quadtree indices
- **Geographic projections** beyond WGS84/EPSG:4326
- **Adaptive zoom levels** based on data density

---

## Previous Releases

### [0.29.1] - Previous Release
- Standard Lance functionality
- Vector search capabilities
- Columnar data format optimizations
