// Test to verify GeoPage encoder selection works correctly

#[cfg(test)]
mod tests {
    use std::collections::HashMap;
    use std::sync::Arc;
    
    use arrow_array::{Float64Array, ArrayRef};
    use arrow_schema::DataType;
    use lance_core::datatypes::Field;
    
    use crate::encoder::{ArrayEncodingStrategy, CoreArrayEncodingStrategy};
    use crate::version::LanceFileVersion;

    #[test]
    fn test_geopage_encoder_selection() {
        // Create test data (longitude/latitude coordinates)
        let lons = vec![-122.5, -122.4, -122.3, -122.2];
        let lats = vec![37.7, 37.8, 37.9, 38.0];
        
        let lon_array = Arc::new(Float64Array::from(lons)) as ArrayRef;
        let lat_array = Arc::new(Float64Array::from(lats)) as ArrayRef;
        
        // Test 1: Standard field without encoding metadata (should use default encoder)
        let mut standard_field = Field::new("coordinates", DataType::Float64, false);
        let encoding_strategy = CoreArrayEncodingStrategy::default();
        
        let standard_encoder = encoding_strategy.create_array_encoder(&[lon_array.clone()], &standard_field);
        assert!(standard_encoder.is_ok());
        println!("✅ Standard encoder selection works");
        
        // Test 2: Field with GeoPage encoding metadata (should use GeoPageEncoder)
        let mut geopage_metadata = HashMap::new();
        geopage_metadata.insert("encoding".to_string(), "geopage".to_string());
        standard_field.set_metadata(geopage_metadata);
        
        let geopage_encoder = encoding_strategy.create_array_encoder(&[lon_array.clone()], &standard_field);
        assert!(geopage_encoder.is_ok());
        
        // Verify it's actually a GeoPageEncoder by checking the debug string
        let encoder_debug = format!("{:?}", geopage_encoder.unwrap());
        assert!(encoder_debug.contains("GeoPageEncoder"));
        println!("✅ GeoPage encoder selection works");
        
        // Test 3: Field with different encoding (should use default encoder)
        let mut other_metadata = HashMap::new();
        other_metadata.insert("encoding".to_string(), "fsst".to_string());
        standard_field.set_metadata(other_metadata);
        
        let other_encoder = encoding_strategy.create_array_encoder(&[lon_array.clone()], &standard_field);
        assert!(other_encoder.is_ok());
        
        let other_debug = format!("{:?}", other_encoder.unwrap());
        assert!(!other_debug.contains("GeoPageEncoder"));
        println!("✅ Non-GeoPage encoding falls back to default");
        
        println!("🎯 ALL TESTS PASSED: GeoPage encoder selection working correctly!");
        println!("🚀 Python 2-liner should now work: lance.write_dataset(data, 'file.lance', encoding='geopage')");
    }
}
