name: Bump version for releases
description: "Call bumpversion"
inputs:
  part:
    description: "What kind of release is this?"
    required: true
    default: "release"
runs:
  using: "composite"
  steps:
    - name: Set git configs for bumpversion
      shell: bash
      run: |
        git config user.name 'Lance Release'
        git config user.email '<EMAIL>'
    - name: Create release version and tags
      working-directory: python
      shell: bash
      run: |
        cargo install cargo-bump
        cargo bump ${{ inputs.part }}
    - name: Synchronize rust version
      shell: bash
      run: |
        cargo install cargo-workspaces --version 0.2.44
        cargo ws version --no-git-commit -y --exact --force 'lance*' ${{ inputs.part }}
    - name: Update python lockfile
      working-directory: python
      shell: bash
      run: |
        cargo update -p lance
    - name: Bump java version
      working-directory: java
      shell: bash
      run: |
        # Get current version
        current_version=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)
        current_version=${current_version%%}

        base_version="${current_version%-*}"
        if [[ "$current_version" == *-* ]]; then
          suffix="-${current_version#*-}"
        else
          suffix=""
        fi

        # Split the version into components using parameter expansion
        IFS=. read major minor patch <<<"$base_version"

        case "${{ inputs.part }}" in
          patch)
            patch=$((patch + 1))
            ;;
          minor)
            minor=$((minor + 1))
            patch=0
            ;;
          major)
            major=$((major + 1))
            minor=0
            patch=0
            ;;
          *)
            echo "Invalid part specified"
            exit 1
            ;;
        esac

        new_version="${major}.${minor}.${patch}${suffix}"

        mvn versions:set versions:commit -DnewVersion=$new_version
