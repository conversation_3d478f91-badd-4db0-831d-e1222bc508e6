name: Deploy docs to Pages

on:
  push:
    branches: ["main"]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# Allow one concurrent deployment
concurrency:
  group: "pages"
  cancel-in-progress: true

env:
  RUSTFLAGS: "-C debuginfo=0"
  # according to: https://matklad.github.io/2021/09/04/fast-rust-builds.html
  # CI builds are faster with incremental disabled.
  CARGO_INCREMENTAL: "0"

jobs:
  # Single deploy job since we're just deploying
  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"
          cache: 'pip'
          cache-dependency-path: "docs/requirements.txt"
      - name: Install dependencies
        run: |
          sudo apt install -y -qq doxygen pandoc
      - name: Build python wheel
        uses: ./.github/workflows/build_linux_wheel
      - name: Free disk space
        working-directory: python
        run: |
          sudo chown 1001:118 -R target
          mv target/wheels/*.whl ./
          cargo clean
      - name: Build Python
        working-directory: python
        run: |
          python -m pip install ../python/*.whl
          python -m pip install -r ../docs/requirements.txt
      - name: Build docs
        working-directory: docs
        run: |
          make nbconvert
          make html
      - name: Setup Pages
        uses: actions/configure-pages@v5
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: 'docs/_build/html'
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
