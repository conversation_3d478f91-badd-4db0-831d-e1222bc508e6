name: Build and publish Java packages
on:
  release:
    # Use released instead of published, since we don't publish preview/beta
    # versions
    types: [released]
  pull_request:
    paths:
      - .github/workflows/java-publish.yml
  workflow_dispatch:
    inputs:
      mode:
        description: 'Release mode'
        required: true
        type: choice
        default: dry_run
        options:
          - dry_run
          - release
      ref:
        description: 'The branch, tag or SHA to checkout'
        required: false
        type: string

jobs:
  linux-arm64:
    name: Build on Linux Arm64
    runs-on: ubuntu-2404-8x-arm64
    timeout-minutes: 60
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Check glibc version outside docker
        run: ldd --version
      - name: Build and run in Debian 10 Arm64 container
        run: |
          docker run --platform linux/arm64 -v ${{ github.workspace }}:/workspace -w /workspace debian:10 bash -c "
            
            set -ex
            apt-get update
          
            DEBIAN_FRONTEND=noninteractive apt-get install --no-install-recommends --assume-yes \
              apt-transport-https \
              ca-certificates \
              curl \
              gpg \
              bash \
              less \
              openssl \
              libssl-dev \
              pkg-config \
              libsqlite3-dev \
              libsqlite3-0 \
              libreadline-dev \
              git \
              cmake \
              dh-autoreconf \
              clang \
              g++ \
              libc++-dev \
              libc++abi-dev \
              libprotobuf-dev \
              libncurses5-dev \
              libncursesw5-dev \
              libudev-dev \
              libhidapi-dev \
              zip \
              unzip
            
            # https://github.com/databendlabs/databend/issues/8035
            PROTOC_ZIP=protoc-3.15.0-linux-aarch_64.zip
            curl -OL https://github.com/protocolbuffers/protobuf/releases/download/v3.15.0/\$PROTOC_ZIP
            unzip -o \$PROTOC_ZIP -d /usr/local
            rm -f \$PROTOC_ZIP
            protoc --version
          
            curl https://sh.rustup.rs -sSf | sh -s -- -y --default-toolchain stable
            source \$HOME/.cargo/env
            cargo --version
            
            cd java
            
            # https://github.com/rustls/rustls/issues/1967
            export CC=clang
            export CXX=clang++
            ldd --version

            cargo build --release
          "
      - uses: actions/upload-artifact@v4
        with:
          name: liblance_jni_linux_arm_64.zip
          path: target/release/liblance_jni.so
          retention-days: 1
          if-no-files-found: error
  linux-x86:
    name: Build on Linux x86-64
    runs-on: ubuntu-24.04
    timeout-minutes: 60
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Check glibc version outside docker
        run: ldd --version
      - name: Build and run in Debian 10 X86-64 container
        run: |
          docker run --platform linux/amd64 -v ${{ github.workspace }}:/workspace -w /workspace debian:10 bash -c "
          
            set -ex
            apt-get update
          
            DEBIAN_FRONTEND=noninteractive apt-get install --no-install-recommends --assume-yes \
              apt-transport-https \
              ca-certificates \
              curl \
              gpg \
              bash \
              less \
              openssl \
              libssl-dev \
              pkg-config \
              libsqlite3-dev \
              libsqlite3-0 \
              libreadline-dev \
              git \
              cmake \
              dh-autoreconf \
              clang \
              g++ \
              libc++-dev \
              libc++abi-dev \
              libprotobuf-dev \
              libncurses5-dev \
              libncursesw5-dev \
              libudev-dev \
              libhidapi-dev \
              zip \
              unzip
          
            # https://github.com/databendlabs/databend/issues/8035
            PROTOC_ZIP=protoc-3.15.0-linux-x86_64.zip
            curl -OL https://github.com/protocolbuffers/protobuf/releases/download/v3.15.0/\$PROTOC_ZIP
            unzip -o \$PROTOC_ZIP -d /usr/local
            rm -f \$PROTOC_ZIP
            protoc --version
          
            curl https://sh.rustup.rs -sSf | sh -s -- -y --default-toolchain stable
            source \$HOME/.cargo/env
            cargo --version
          
            cd java
          
            # https://github.com/rustls/rustls/issues/1967
            export CC=clang
            export CXX=clang++
            ldd --version

            cargo build --release
          "
      - uses: actions/upload-artifact@v4
        with:
          name: liblance_jni_linux_x86_64.zip
          path: target/release/liblance_jni.so
          retention-days: 1
          if-no-files-found: error
  macos-arm64:
    name: Build on MacOS Arm64 and release
    runs-on: macos-14
    timeout-minutes: 60
    needs:
      - linux-arm64
      - linux-x86
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}
      - uses: Swatinem/rust-cache@v2
      - name: Set up Java 8
        uses: actions/setup-java@v4
        with:
          distribution: corretto
          java-version: 8
          cache: "maven"
          server-id: ossrh
          server-username: SONATYPE_USER
          server-password: SONATYPE_TOKEN
          gpg-private-key: ${{ secrets.GPG_PRIVATE_KEY }}
          gpg-passphrase: ${{ secrets.GPG_PASSPHRASE }}
      - uses: Homebrew/actions/setup-homebrew@master
      - name: Install dependencies
        run: |
          brew install protobuf
          brew install gpg
      - name: Download artifact
        uses: actions/download-artifact@v4
      - name: Copy native libs
        run: |
          mkdir -p ./java/core/target/classes/nativelib/linux-x86-64 ./java/core/target/classes/nativelib/linux-aarch64
          cp ./liblance_jni_linux_x86_64.zip/liblance_jni.so ./java/core/target/classes/nativelib/linux-x86-64/liblance_jni.sp
          cp ./liblance_jni_linux_arm_64.zip/liblance_jni.so ./java/core/target/classes/nativelib/linux-aarch64/liblance_jni.so
      - name: Set github
        run: |
          git config --global user.email "Lance Github Runner"
          git config --global user.name "<EMAIL>"
      - name: Dry run
        if: |
          github.event_name == 'pull_request' ||
          inputs.mode == 'dry_run'
        working-directory: java
        run: |
          mvn --batch-mode -DskipTests -Drust.release.build=true package
      - name: Publish with Java 8
        if: |
          github.event_name == 'release' ||
          inputs.mode == 'release'
        working-directory: java
        run: |
          echo "use-agent" >> ~/.gnupg/gpg.conf
          echo "pinentry-mode loopback" >> ~/.gnupg/gpg.conf
          export GPG_TTY=$(tty)
          mvn --batch-mode -DskipTests -Drust.release.build=true -DpushChanges=false -Dgpg.passphrase=${{ secrets.GPG_PASSPHRASE }} deploy -P deploy-to-ossrh -P shade-jar
        env:
          SONATYPE_USER: ${{ secrets.SONATYPE_USER }}
          SONATYPE_TOKEN: ${{ secrets.SONATYPE_TOKEN }}