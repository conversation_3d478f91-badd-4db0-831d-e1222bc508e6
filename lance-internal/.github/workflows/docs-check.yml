name: Check docs

on:
  push:
    branches: ["main"]
  pull_request:
    paths:
      - docs/**
      - python/python/**
      - .github/workflows/docs-check.yml

env:
  RUSTFLAGS: "-C debuginfo=0"
  # according to: https://matklad.github.io/2021/09/04/fast-rust-builds.html
  # CI builds are faster with incremental disabled.
  CARGO_INCREMENTAL: "0"

jobs:
  # Single deploy job since we're just deploying
  check-docs:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"
          cache: 'pip'
          cache-dependency-path: "docs/requirements.txt"
      - name: Install dependencies
        run: |
          sudo apt install -y -qq doxygen pandoc
      - name: Build python wheel
        uses: ./.github/workflows/build_linux_wheel
      - name: Free disk space
        working-directory: python
        run: |
          sudo chown 1001:118 -R target
          mv target/wheels/*.whl ./
          cargo clean
      - name: Build Python
        working-directory: docs
        run: |
          python -m pip install ../python/*.whl
          python -m pip install -r requirements.txt
      - name: Run test
        working-directory: docs
        run: |
          make doctest
      - name: Build docs
        working-directory: docs
        run: |
          make nbconvert
          make html
      - name: Check links
        working-directory: docs
        run: |
          make linkcheck

