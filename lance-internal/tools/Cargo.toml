[package]
name = "geopage-cli"
version = "0.1.0"
edition = "2021"
authors = ["Terrafloww Labs"]
description = "CLI tool for testing GeoPage codec with real geospatial data"

[[bin]]
name = "geopage_cli"
path = "geopage_cli.rs"

[dependencies]
# Lance dependencies
lance-core = { path = "../rust/lance-core" }
lance-encoding = { path = "../rust/lance-encoding" }
lance-file = { path = "../rust/lance-file" }
lance-table = { path = "../rust/lance-table" }

# Arrow dependencies
arrow-array = "55.1"
arrow-schema = "55.1"
arrow-ipc = "55.1"

# CLI dependencies
clap = { version = "4", features = ["derive"] }
env_logger = "0.11"
log = "0.4"
snafu = "0.8"
tempfile = "3"

# Async runtime
tokio = { version = "1.23", features = ["rt-multi-thread", "macros", "fs"] }

[dev-dependencies]
tempfile = "3"
