[package]
name = "lance-jni"
version.workspace = true
edition.workspace = true
repository.workspace = true
readme.workspace = true
license.workspace = true
description = "JNI bindings for Lance Columnar format"
keywords.workspace = true
categories.workspace = true

[lib]
crate-type = ["cdylib"]

[dependencies]
lance = { workspace = true, features = ["substrait"] }
lance-datafusion = { workspace = true }
lance-encoding = { workspace = true }
lance-linalg = { workspace = true }
lance-index = { workspace = true }
lance-io.workspace = true
lance-core.workspace = true
lance-file.workspace = true
arrow = { workspace = true, features = ["ffi"] }
arrow-schema.workspace = true
datafusion.workspace = true
object_store.workspace = true
tokio.workspace = true
jni = "0.21.1"
snafu.workspace = true
lazy_static.workspace = true
serde = { version = "^1" }
serde_json = { version = "1" }
log.workspace = true
env_logger = "0.11.7"
