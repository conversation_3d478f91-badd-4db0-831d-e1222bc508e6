<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.lancedb</groupId>
        <artifactId>lance-parent</artifactId>
        <version>0.29.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>lance-core</artifactId>
    <name>Lance Core</name>
    <packaging>jar</packaging>
    <properties>
        <rust.release.build>false</rust.release.build>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.apache.arrow</groupId>
            <artifactId>arrow-vector</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.arrow</groupId>
            <artifactId>arrow-memory-netty</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.arrow</groupId>
            <artifactId>arrow-c-data</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.arrow</groupId>
            <artifactId>arrow-dataset</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.questdb</groupId>
            <artifactId>jar-jni</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
             <scope>test</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>build-jni</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.questdb</groupId>
                        <artifactId>rust-maven-plugin</artifactId>
                        <version>1.1.1</version>
                        <executions>
                            <execution>
                                <id>lance-jni</id>
                                <goals>
                                    <goal>build</goal>
                                </goals>
                                <configuration>
                                    <path>lance-jni</path>
                                    <release>${rust.release.build}</release>
                                    <!-- Copy native libraries to target/classes for runtime access -->
                                    <copyTo>${project.build.directory}/classes/nativelib</copyTo>
                                    <copyWithPlatformDir>true</copyWithPlatformDir>
                                </configuration>
                            </execution>
                            <execution>
                                <id>lance-jni-test</id>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                                <configuration>
                                    <path>lance-jni</path>
                                    <release>${rust.release.build}</release>
                                    <verbosity>-v</verbosity>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
