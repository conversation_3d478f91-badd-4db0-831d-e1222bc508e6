# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright The Lance Authors

import platform
import random
import string
import time
from pathlib import Path

import lance
import numpy as np
import pyarrow as pa
import pyarrow.compute as pc
import pytest
from lance import LanceFragment
from lance.dataset import VectorIndexReader
from lance.util import validate_vector_index  # noqa: E402
from lance.vector import vec_to_table  # noqa: E402


def create_table(nvec=1000, ndim=128, nans=0, nullify=False, dtype=np.float32):
    mat = np.random.randn(nvec, ndim)
    if nans > 0:
        nans_mat = np.empty((nans, ndim))
        nans_mat[:] = np.nan
        mat = np.concatenate((mat, nans_mat), axis=0)
    mat = mat.astype(dtype)
    price = np.random.rand(nvec + nans) * 100

    def gen_str(n):
        return "".join(random.choices(string.ascii_letters + string.digits, k=n))

    meta = np.array([gen_str(100) for _ in range(nvec + nans)])
    tbl = (
        vec_to_table(data=mat)
        .append_column("price", pa.array(price))
        .append_column("meta", pa.array(meta))
        .append_column("id", pa.array(range(nvec + nans)))
    )
    if nullify:
        idx = tbl.schema.get_field_index("vector")
        vecs = tbl[idx].to_pylist()
        nullified = [vec if i % 2 == 0 else None for i, vec in enumerate(vecs)]
        field = tbl.schema.field(idx)
        vecs = pa.array(nullified, field.type)
        tbl = tbl.set_column(idx, field, vecs)
    return tbl


def create_multivec_table(
    nvec=1000, nvec_per_row=5, ndim=128, nans=0, nullify=False, dtype=np.float32
):
    mat = np.random.randn(nvec, nvec_per_row, ndim)
    if nans > 0:
        nans_mat = np.empty((nans, ndim))
        nans_mat[:] = np.nan
        mat = np.concatenate((mat, nans_mat), axis=0)
    mat = mat.astype(dtype)
    price = np.random.rand(nvec + nans) * 100

    def gen_str(n):
        return "".join(random.choices(string.ascii_letters + string.digits, k=n))

    meta = np.array([gen_str(100) for _ in range(nvec + nans)])

    multi_vec_type = pa.list_(pa.list_(pa.float32(), ndim))
    tbl = pa.Table.from_arrays(
        [
            pa.array((mat[i].tolist() for i in range(nvec)), type=multi_vec_type),
        ],
        schema=pa.schema(
            [
                pa.field("vector", pa.list_(pa.list_(pa.float32(), ndim))),
            ]
        ),
    )
    tbl = (
        tbl.append_column("price", pa.array(price))
        .append_column("meta", pa.array(meta))
        .append_column("id", pa.array(range(nvec + nans)))
    )
    if nullify:
        idx = tbl.schema.get_field_index("vector")
        vecs = tbl[idx].to_pylist()
        nullified = [vec if i % 2 == 0 else None for i, vec in enumerate(vecs)]
        field = tbl.schema.field(idx)
        vecs = pa.array(nullified, field.type)
        tbl = tbl.set_column(idx, field, vecs)
    return tbl


@pytest.fixture()
def dataset(tmp_path):
    tbl = create_table()
    yield lance.write_dataset(tbl, tmp_path)


@pytest.fixture()
def indexed_dataset(tmp_path):
    tbl = create_table()
    dataset = lance.write_dataset(tbl, tmp_path)
    yield dataset.create_index(
        "vector", index_type="IVF_PQ", num_partitions=4, num_sub_vectors=16
    )


@pytest.fixture()
def multivec_dataset():
    tbl = create_multivec_table()
    yield lance.write_dataset(tbl, "memory://")


@pytest.fixture()
def indexed_multivec_dataset(multivec_dataset):
    yield multivec_dataset.create_index(
        "vector",
        index_type="IVF_PQ",
        num_partitions=4,
        num_sub_vectors=16,
        metric="cosine",
    )


def run(ds, q=None, assert_func=None):
    if q is None:
        q = np.random.randn(128)
    project = [None, ["price"], ["vector", "price"], ["vector", "meta", "price"]]
    refine = [None, 1, 2]
    filters = [None, pc.field("price") > 50.0]
    times = []

    for columns in project:
        expected_columns = []
        if columns is None:
            expected_columns.extend(ds.schema.names)
        else:
            expected_columns.extend(columns)
        # TODO: _distance shouldn't be returned by default either
        if "_distance" not in expected_columns:
            expected_columns.append("_distance")

        for filter_ in filters:
            for rf in refine:
                start = time.time()
                rs = ds.to_table(
                    columns=columns,
                    nearest={
                        "column": "vector",
                        "q": q,
                        "k": 15,
                        "nprobes": 1,
                        "refine_factor": rf,
                    },
                    filter=filter_,
                )
                end = time.time()
                times.append(end - start)
                assert rs.column_names == expected_columns
                if filter_ is not None:
                    inmem = pa.dataset.dataset(rs)
                    assert len(inmem.to_table(filter=filter_)) == len(rs)
                else:
                    assert len(rs) == 15
                    distances = rs["_distance"].to_numpy()
                    assert (distances.max() - distances.min()) > 1e-6
                    if assert_func is not None:
                        assert_func(rs)
    return times


def test_flat(dataset):
    run(dataset)


def test_ann(indexed_dataset):
    run(indexed_dataset)


def test_ann_append(tmp_path):
    tbl = create_table()
    dataset = lance.write_dataset(tbl, tmp_path)
    dataset = dataset.create_index(
        "vector", index_type="IVF_PQ", num_partitions=4, num_sub_vectors=16
    )
    new_data = create_table(nvec=10)
    dataset = lance.write_dataset(new_data, dataset.uri, mode="append")
    q = new_data["vector"][0].as_py()

    def func(rs: pa.Table):
        if "vector" not in rs:
            return
        assert rs["vector"][0].as_py() == q

    run(dataset, q=np.array(q), assert_func=func)


def test_invalid_subvectors(tmp_path):
    tbl = create_table()
    dataset = lance.write_dataset(tbl, tmp_path)
    with pytest.raises(
        ValueError,
        match="dimension .* must be divisible by num_sub_vectors",
    ):
        dataset.create_index(
            "vector", index_type="IVF_PQ", num_partitions=4, num_sub_vectors=15
        )


@pytest.mark.cuda
def test_invalid_subvectors_cuda(tmp_path):
    tbl = create_table()
    dataset = lance.write_dataset(tbl, tmp_path)
    with pytest.raises(
        ValueError,
        match="dimension .* must be divisible by num_sub_vectors",
    ):
        dataset.create_index(
            "vector",
            index_type="IVF_PQ",
            num_partitions=4,
            num_sub_vectors=15,
            accelerator="cuda",
        )


@pytest.mark.cuda
def test_f16_cuda(tmp_path):
    tbl = create_table(dtype=np.float16)
    dataset = lance.write_dataset(tbl, tmp_path)
    dataset = dataset.create_index(
        "vector",
        index_type="IVF_PQ",
        num_partitions=4,
        num_sub_vectors=16,
        accelerator="cuda",
        one_pass_ivfpq=True,
    )
    validate_vector_index(dataset, "vector")


def test_index_with_nans(tmp_path):
    # 1024 rows, the entire table should be sampled
    tbl = create_table(nvec=1000, nans=24)

    dataset = lance.write_dataset(tbl, tmp_path)
    dataset = dataset.create_index(
        "vector",
        index_type="IVF_PQ",
        num_partitions=4,
        num_sub_vectors=16,
    )
    validate_vector_index(dataset, "vector")


def test_torch_index_with_nans(tmp_path):
    torch = pytest.importorskip("torch")

    # 1024 rows, the entire table should be sampled
    tbl = create_table(nvec=1000, nans=24)

    dataset = lance.write_dataset(tbl, tmp_path)
    dataset = dataset.create_index(
        "vector",
        index_type="IVF_PQ",
        num_partitions=4,
        num_sub_vectors=16,
        accelerator=torch.device("cpu"),
        one_pass_ivfpq=True,
    )
    validate_vector_index(dataset, "vector")


def test_index_with_no_centroid_movement(tmp_path):
    torch = pytest.importorskip("torch")

    # this test makes the centroids essentially [1..]
    # this makes sure the early stop condition in the index building code
    # doesn't do divide by zero
    mat = np.concatenate([np.ones((256, 32))])

    tbl = vec_to_table(data=mat)

    dataset = lance.write_dataset(tbl, tmp_path)
    dataset = dataset.create_index(
        "vector",
        index_type="IVF_PQ",
        num_partitions=1,
        num_sub_vectors=4,
        accelerator=torch.device("cpu"),
    )
    validate_vector_index(dataset, "vector")


def test_index_with_pq_codebook(tmp_path):
    tbl = create_table(nvec=1024, ndim=128)
    dataset = lance.write_dataset(tbl, tmp_path)
    pq_codebook = np.random.randn(4, 256, 128 // 4).astype(np.float32)

    dataset = dataset.create_index(
        "vector",
        index_type="IVF_PQ",
        num_partitions=1,
        num_sub_vectors=4,
        ivf_centroids=np.random.randn(1, 128).astype(np.float32),
        pq_codebook=pq_codebook,
    )
    validate_vector_index(dataset, "vector", refine_factor=10, pass_threshold=0.99)

    pq_codebook = pa.FixedShapeTensorArray.from_numpy_ndarray(pq_codebook)

    dataset = dataset.create_index(
        "vector",
        index_type="IVF_PQ",
        num_partitions=1,
        num_sub_vectors=4,
        ivf_centroids=np.random.randn(1, 128).astype(np.float32),
        pq_codebook=pq_codebook,
        replace=True,
    )
    validate_vector_index(dataset, "vector", refine_factor=10, pass_threshold=0.99)


@pytest.mark.cuda
@pytest.mark.parametrize("nullify", [False, True])
def test_create_index_using_cuda(tmp_path, nullify):
    tbl = create_table(nullify=nullify)
    dataset = lance.write_dataset(tbl, tmp_path)
    dataset = dataset.create_index(
        "vector",
        index_type="IVF_PQ",
        num_partitions=4,
        num_sub_vectors=16,
        accelerator="cuda",
    )
    q = np.random.randn(128)
    expected = dataset.to_table(
        columns=["id"],
        nearest={
            "column": "vector",
            "q": q,
            "k": 10,  # Use non-default k
        },
    )["id"].to_numpy()
    assert len(expected) == 10


def test_create_index_unsupported_accelerator(tmp_path):
    # Even attempting to use an accelerator will trigger torch import
    # so make sure it's available
    pytest.importorskip("torch")

    tbl = create_table()
    dataset = lance.write_dataset(tbl, tmp_path)
    with pytest.raises(ValueError):
        dataset.create_index(
            "vector",
            index_type="IVF_PQ",
            num_partitions=4,
            num_sub_vectors=16,
            accelerator="no-supported",
        )

    with pytest.raises(ValueError):
        dataset.create_index(
            "vector",
            index_type="IVF_PQ",
            num_partitions=4,
            num_sub_vectors=16,
            accelerator="0cuda",
        )

    with pytest.raises(ValueError):
        dataset.create_index(
            "vector",
            index_type="IVF_PQ",
            num_partitions=4,
            num_sub_vectors=16,
            accelerator="cuda-0",
        )

    with pytest.raises(ValueError):
        dataset.create_index(
            "vector",
            index_type="IVF_PQ",
            num_partitions=4,
            num_sub_vectors=16,
            accelerator="cuda:",
        )

    with pytest.raises(ValueError):
        dataset.create_index(
            "vector",
            index_type="IVF_PQ",
            num_partitions=4,
            num_sub_vectors=16,
            accelerator="cuda:abc",
        )


def test_use_index(dataset, tmp_path):
    ann_ds = lance.write_dataset(dataset.to_table(), tmp_path / "indexed.lance")
    ann_ds = ann_ds.create_index(
        "vector", index_type="IVF_PQ", num_partitions=4, num_sub_vectors=16
    )
    q = np.random.randn(128)
    expected = dataset.to_table(
        columns=["id"],
        nearest={
            "column": "vector",
            "q": q,
            "k": 12,  # Use non-default k
        },
    )["id"].to_numpy()

    actual = ann_ds.to_table(
        columns=["id"],
        nearest={"column": "vector", "q": q, "k": 12, "use_index": False},
    )["id"].to_numpy()

    assert np.all(expected == actual)

    # Can omit k but provide limit
    actual = ann_ds.to_table(
        columns=["id"],
        nearest={"column": "vector", "q": q, "use_index": False},
        limit=12,
    )["id"].to_numpy()
    assert np.all(expected == actual)


def test_nearest_errors(dataset, tmp_path):
    import pandas as pd

    with pytest.raises(ValueError, match="does not match index column size"):
        dataset.to_table(
            columns=["id"],
            nearest={"column": "vector", "q": np.random.randn(127), "k": 10},
        )

    df = pd.DataFrame({"a": [5], "b": [10]})
    ds = lance.write_dataset(pa.Table.from_pandas(df), tmp_path / "dataset.lance")

    with pytest.raises(TypeError, match="must be a vector"):
        ds.to_table(nearest={"column": "a", "q": np.random.randn(128), "k": 10})


def test_has_index(dataset, tmp_path):
    assert not dataset.has_index
    ann_ds = lance.write_dataset(dataset.to_table(), tmp_path / "indexed.lance")
    ann_ds = ann_ds.create_index(
        "vector", index_type="IVF_PQ", num_partitions=4, num_sub_vectors=16
    )
    assert ann_ds.has_index

    assert ann_ds.list_indices()[0]["fields"] == ["vector"]


def test_index_type(dataset, tmp_path):
    ann_ds = lance.write_dataset(dataset.to_table(), tmp_path / "indexed.lance")

    ann_ds = ann_ds.create_index(
        "vector",
        index_type="IVF_PQ",
        num_partitions=4,
        num_sub_vectors=16,
        replace=True,
    )
    assert ann_ds.list_indices()[0]["type"] == "IVF_PQ"

    ann_ds = ann_ds.create_index(
        "vector",
        index_type="IVF_HNSW_SQ",
        num_partitions=4,
        num_sub_vectors=16,
        replace=True,
    )
    assert ann_ds.list_indices()[0]["type"] == "IVF_HNSW_SQ"

    ann_ds = ann_ds.create_index(
        "vector",
        index_type="IVF_HNSW_PQ",
        num_partitions=4,
        num_sub_vectors=16,
        replace=True,
    )
    assert ann_ds.list_indices()[0]["type"] == "IVF_HNSW_PQ"


def test_create_dot_index(dataset, tmp_path):
    assert not dataset.has_index
    ann_ds = lance.write_dataset(dataset.to_table(), tmp_path / "indexed.lance")
    ann_ds = ann_ds.create_index(
        "vector",
        index_type="IVF_PQ",
        num_partitions=4,
        num_sub_vectors=16,
        metric="dot",
    )
    assert ann_ds.has_index


def test_create_4bit_ivf_pq_index(dataset, tmp_path):
    assert not dataset.has_index
    ann_ds = lance.write_dataset(dataset.to_table(), tmp_path / "indexed.lance")
    ann_ds = ann_ds.create_index(
        "vector",
        index_type="IVF_PQ",
        num_partitions=1,
        num_sub_vectors=16,
        num_bits=4,
        metric="l2",
    )
    index = ann_ds.stats.index_stats("vector_idx")
    assert index["indices"][0]["sub_index"]["nbits"] == 4


def test_ivf_flat_over_binary_vector(tmp_path):
    dim = 128
    nvec = 1000
    data = np.random.randint(0, 256, (nvec, dim // 8)).tolist()
    array = pa.array(data, type=pa.list_(pa.uint8(), dim // 8))
    tbl = pa.Table.from_pydict({"vector": array})
    ds = lance.write_dataset(tbl, tmp_path)
    ds.create_index("vector", index_type="IVF_FLAT", num_partitions=4, metric="hamming")
    stats = ds.stats.index_stats("vector_idx")
    assert stats["indices"][0]["metric_type"] == "hamming"
    assert stats["index_type"] == "IVF_FLAT"

    query = np.random.randint(0, 256, dim // 8).astype(np.uint8)
    ds.to_table(
        nearest={
            "column": "vector",
            "q": query,
            "k": 10,
            "metric": "hamming",
        }
    )


def test_create_ivf_hnsw_pq_index(dataset, tmp_path):
    assert not dataset.has_index
    ann_ds = lance.write_dataset(dataset.to_table(), tmp_path / "indexed.lance")
    ann_ds = ann_ds.create_index(
        "vector",
        index_type="IVF_HNSW_PQ",
        num_partitions=4,
        num_sub_vectors=16,
    )
    assert ann_ds.list_indices()[0]["fields"] == ["vector"]


def test_create_ivf_hnsw_sq_index(dataset, tmp_path):
    assert not dataset.has_index
    ann_ds = lance.write_dataset(dataset.to_table(), tmp_path / "indexed.lance")
    ann_ds = ann_ds.create_index(
        "vector",
        index_type="IVF_HNSW_SQ",
        num_partitions=4,
        num_sub_vectors=16,
    )
    assert ann_ds.list_indices()[0]["fields"] == ["vector"]


def test_multivec_ann(indexed_multivec_dataset: lance.LanceDataset):
    query = np.random.rand(5, 128)
    results = indexed_multivec_dataset.scanner(
        nearest={"column": "vector", "q": query, "k": 100}
    ).to_table()
    assert results.num_rows == 100
    assert results["vector"].type == pa.list_(pa.list_(pa.float32(), 128))
    assert len(results["vector"][0]) == 5

    # query with single vector also works
    query = np.random.rand(128)
    results = indexed_multivec_dataset.to_table(
        nearest={"column": "vector", "q": query, "k": 100}
    )
    # we don't verify the number of results here,
    # because for multivector, it's not guaranteed to return k results
    assert results["vector"].type == pa.list_(pa.list_(pa.float32(), 128))
    assert len(results["vector"][0]) == 5

    query = [query, query]
    doubled_results = indexed_multivec_dataset.to_table(
        nearest={"column": "vector", "q": query, "k": 100}
    )
    assert len(results) == len(doubled_results)
    for i in range(len(results)):
        assert (
            results["_distance"][i].as_py() * 2
            == doubled_results["_distance"][i].as_py()
        )

    # query with a vector that dim not match
    query = np.random.rand(256)
    with pytest.raises(ValueError, match="does not match index column size"):
        indexed_multivec_dataset.to_table(
            nearest={"column": "vector", "q": query, "k": 100}
        )

    # query with a list of vectors that some dim not match
    query = [np.random.rand(128)] * 5 + [np.random.rand(256)]
    with pytest.raises(ValueError, match="All query vectors must have the same length"):
        indexed_multivec_dataset.to_table(
            nearest={"column": "vector", "q": query, "k": 100}
        )


def test_pre_populated_ivf_centroids(dataset, tmp_path: Path):
    centroids = np.random.randn(5, 128).astype(np.float32)  # IVF5
    dataset_with_index = dataset.create_index(
        ["vector"],
        index_type="IVF_PQ",
        metric="cosine",
        ivf_centroids=centroids,
        num_partitions=5,
        num_sub_vectors=8,
    )

    q = np.random.randn(128)
    actual = dataset_with_index.to_table(
        columns=["id"],
        nearest={"column": "vector", "q": q, "k": 10, "use_index": False},
    )["id"].to_numpy()
    assert len(actual) == 10

    index_meta = dataset_with_index.list_indices()[0]
    index_uuid = index_meta["uuid"]
    assert len(index_uuid) == 36
    assert index_meta["fragment_ids"] == {0}

    expected_filepath = str(tmp_path / "_indices" / index_uuid / "index.idx")
    if platform.system() == "Windows":
        expected_filepath = expected_filepath.replace("\\", "/")
    expected_statistics = {
        "index_type": "IVF_PQ",
        "uuid": index_uuid,
        "uri": expected_filepath,
        "metric_type": "cosine",
        "num_partitions": 5,
        "sub_index": {
            "dimension": 128,
            "index_type": "PQ",
            "metric_type": "l2",
            "nbits": 8,
            "num_sub_vectors": 8,
            "transposed": True,
        },
    }

    with pytest.raises(KeyError, match='Index "non-existent_idx" not found'):
        # increase 1 miss of index_cache.metadata_cache
        assert dataset_with_index.stats.index_stats("non-existent_idx")
    with pytest.raises(KeyError, match='Index "" not found'):
        # increase 1 miss of index_cache.metadata_cache
        assert dataset_with_index.stats.index_stats("")
    with pytest.raises(TypeError):
        dataset_with_index.stats.index_stats()

    # increase 1 hit of index_cache.metadata_cache
    actual_statistics = dataset_with_index.stats.index_stats("vector_idx")
    assert actual_statistics["num_indexed_rows"] == 1000
    assert actual_statistics["num_unindexed_rows"] == 0

    idx_stats = actual_statistics["indices"][0]
    partitions = idx_stats.pop("partitions")
    idx_stats.pop("centroids")
    idx_stats.pop("loss")
    assert idx_stats == expected_statistics
    assert len(partitions) == 5
    partition_keys = {"size"}
    assert all([partition_keys == set(p.keys()) for p in partitions])


def test_optimize_index(dataset, tmp_path):
    dataset_uri = tmp_path / "dataset.lance"
    assert not dataset.has_index
    ds = lance.write_dataset(dataset.to_table(), dataset_uri)
    ds = ds.create_index(
        "vector",
        index_type="IVF_PQ",
        num_partitions=4,
        num_sub_vectors=2,
    )

    assert ds.has_index

    # New data
    tbl = create_table(nvec=200)
    ds = lance.write_dataset(tbl, dataset_uri, mode="append")

    assert len(ds) == 1200
    assert ds.has_index

    indices_dir = dataset_uri / "_indices"
    assert len(list(indices_dir.iterdir())) == 1

    ds = ds.optimize.optimize_indices()
    assert len(list(indices_dir.iterdir())) == 2


def test_optimize_index_cosine(dataset, tmp_path):
    dataset_uri = tmp_path / "dataset.lance"
    assert not dataset.has_index
    ds = lance.write_dataset(dataset.to_table(), dataset_uri)
    ds = ds.create_index(
        "vector",
        metric="cosine",
        index_type="IVF_PQ",
        num_partitions=4,
        num_sub_vectors=2,
    )

    assert len(ds) == 1000
    assert ds.has_index

    n_results_before_append = ds.to_table(
        nearest={
            "q": [0.1 for _ in range(128)],
            "column": "vector",
            "k": len(ds),
            "nprobes": 1,
        },
        fast_search=True,
    ).num_rows

    # New data
    tbl = create_table(nvec=200)
    ds = lance.write_dataset(tbl, dataset_uri, mode="append")

    assert len(ds) == 1200
    assert ds.has_index

    indices_dir = dataset_uri / "_indices"
    assert len(list(indices_dir.iterdir())) == 1

    # with fast search the index doesn't contain new data yet
    assert (
        ds.to_table(
            nearest={
                "q": [0.1 for _ in range(128)],
                "column": "vector",
                "k": len(ds),
                "nprobes": 1,
            },
            fast_search=True,
        ).num_rows
        == n_results_before_append
    )

    ds.optimize.optimize_indices()
    assert len(list(indices_dir.iterdir())) == 2

    ds = lance.dataset(dataset_uri)

    assert (
        ds.to_table(
            nearest={
                "q": [0.1 for _ in range(128)],
                "column": "vector",
                "k": len(ds),
                "nprobes": 1,
            },
            fast_search=True,
        ).num_rows
        > n_results_before_append
    )


def test_create_index_dot(dataset, tmp_path):
    dataset_uri = tmp_path / "dataset.lance"
    assert not dataset.has_index
    ds = lance.write_dataset(dataset.to_table(), dataset_uri)
    ds = ds.create_index(
        "vector",
        index_type="IVF_PQ",
        metric="dot",
        num_partitions=4,
        num_sub_vectors=2,
    )

    assert ds.has_index
    assert "dot" == ds.stats.index_stats("vector_idx")["indices"][0]["metric_type"]


def create_uniform_table(min, max, nvec, offset, ndim=8):
    mat = np.random.uniform(min, max, (nvec, ndim))
    # rowid = np.arange(offset, offset + nvec)
    tbl = vec_to_table(data=mat)
    tbl = pa.Table.from_pydict(
        {
            "vector": tbl.column(0).chunk(0),
            "filterable": np.arange(offset, offset + nvec),
        }
    )
    return tbl


def test_optimize_index_recall(tmp_path: Path):
    base_dir = tmp_path / "dataset"
    data = create_uniform_table(min=0, max=1, nvec=300, offset=0)

    dataset = lance.write_dataset(data, base_dir, max_rows_per_file=150)
    dataset.create_index(
        "vector", index_type="IVF_PQ", num_partitions=2, num_sub_vectors=2
    )
    assert len(dataset.get_fragments()) == 2

    sample_indices = random.sample(range(300), 50)
    sample_query_indices = sample_indices[0:40]
    sample_delete_indices = sample_indices[40:]
    vecs = data.column("vector").chunk(0)
    sample_queries = [
        {"column": "vector", "q": vecs[i].values, "k": 5} for i in sample_query_indices
    ]
    sample_delete_queries = [
        {"column": "vector", "q": vecs[i].values, "k": 5} for i in sample_delete_indices
    ]

    def has_target(target, results):
        for item in results:
            if item.values == target:
                return True
        return False

    def check_index(has_knn_combined, delete_has_happened):
        for query in sample_queries:
            results = dataset.to_table(nearest=query)
            assert has_target(query["q"], results["vector"])
            plan = dataset.scanner(nearest=query).explain_plan(verbose=True)
            assert ("KNNVectorDistance" in plan) == has_knn_combined
        for query in sample_delete_queries:
            results = dataset.to_table(nearest=query).column("vector")
            assert delete_has_happened != has_target(query["q"], results)

    # Original state is 2 indexed fragments of size 150.  This should not require
    # a combined scan
    check_index(has_knn_combined=False, delete_has_happened=False)

    # Add a new fragment, now a combined scan is required
    extra_data = create_uniform_table(min=1000, max=1001, nvec=100, offset=300)
    dataset = lance.write_dataset(
        extra_data, base_dir, mode="append", max_rows_per_file=100
    )
    check_index(has_knn_combined=True, delete_has_happened=False)

    for row_id in sample_delete_indices:
        dataset.delete(f"filterable == {row_id}")

    # Delete some rows, combined KNN still needed
    check_index(has_knn_combined=True, delete_has_happened=True)

    # Optimize the index, combined KNN should no longer be needed
    dataset.optimize.optimize_indices()
    check_index(has_knn_combined=False, delete_has_happened=True)


def test_knn_with_deletions(tmp_path):
    dims = 5
    values = pa.array(
        [x for val in range(50) for x in [float(val)] * 5], type=pa.float32()
    )
    tbl = pa.Table.from_pydict(
        {
            "vector": pa.FixedSizeListArray.from_arrays(values, dims),
            "filterable": pa.array(range(50)),
        }
    )
    dataset = lance.write_dataset(tbl, tmp_path, max_rows_per_group=10)

    dataset.delete("not (filterable % 5 == 0)")

    # Do KNN with k=100, should return 10 vectors
    expected = [
        [0.0] * 5,
        [5.0] * 5,
        [10.0] * 5,
        [15.0] * 5,
        [20.0] * 5,
        [25.0] * 5,
        [30.0] * 5,
        [35.0] * 5,
        [40.0] * 5,
        [45.0] * 5,
    ]

    results = dataset.to_table(
        nearest={"column": "vector", "q": [0.0] * 5, "k": 100}
    ).column("vector")
    assert len(results) == 10

    assert expected == [r.as_py() for r in results]


def test_index_cache_size(tmp_path):
    rng = np.random.default_rng(seed=42)

    def query_index(ds, ntimes, q=None):
        ndim = ds.schema[0].type.list_size
        for _ in range(ntimes):
            ds.to_table(
                nearest={
                    "column": "vector",
                    "q": q if q is not None else rng.standard_normal(ndim),
                    "minimum_nprobes": 1,
                },
            )

    tbl = create_table(nvec=1024, ndim=16)
    dataset = lance.write_dataset(tbl, tmp_path / "test")

    dataset.create_index(
        "vector",
        index_type="IVF_PQ",
        num_partitions=128,
        num_sub_vectors=2,
        index_cache_size=10,
    )

    indexed_dataset = lance.dataset(tmp_path / "test", index_cache_size=0)
    # when there is no hit, the hit rate is hard coded to 1.0
    assert np.isclose(indexed_dataset._ds.index_cache_hit_rate(), 1.0)
    query_index(indexed_dataset, 1)
    # index cache is size=0, there should be no hit
    assert np.isclose(indexed_dataset._ds.index_cache_hit_rate(), 0.0)

    indexed_dataset = lance.dataset(tmp_path / "test", index_cache_size=1)
    # query using the same vector, we should get a very high hit rate
    # it isn't always exactly 199/200 perhaps because the stats counter
    # is a relaxed atomic counter and may lag behind the true value or perhaps
    # because the cache takes some time to get populated by background threads
    query_index(indexed_dataset, 200, q=rng.standard_normal(16))
    assert indexed_dataset._ds.index_cache_hit_rate() > 0.95

    last_hit_rate = indexed_dataset._ds.index_cache_hit_rate()

    # send a few queries with different vectors, the hit rate should drop
    query_index(indexed_dataset, 128)

    assert last_hit_rate > indexed_dataset._ds.index_cache_hit_rate()


def test_f16_index(tmp_path: Path):
    DIM = 64
    uri = tmp_path / "f16data.lance"
    f16_data = np.random.uniform(0, 1, 2048 * DIM).astype(np.float16)
    fsl = pa.FixedSizeListArray.from_arrays(f16_data, DIM)
    tbl = pa.Table.from_pydict({"vector": fsl})
    dataset = lance.write_dataset(tbl, uri)
    dataset.create_index(
        "vector", index_type="IVF_PQ", num_partitions=4, num_sub_vectors=2
    )

    q = np.random.uniform(0, 1, DIM).astype(np.float16)
    rst = dataset.to_table(
        nearest={
            "column": "vector",
            "q": q,
            "k": 10,
        }
    )

    assert rst.schema.field("vector").type.value_type == pa.float16()
    assert len(rst) == 10


def test_vector_with_nans(tmp_path: Path):
    DIM = 32
    TOTAL = 2048
    data = np.random.uniform(0, 1, TOTAL * DIM).astype(np.float32)

    # Put the 1st vector as NaN.
    np.put(data, range(DIM, 2 * DIM), np.nan)
    fsl = pa.FixedSizeListArray.from_arrays(data, DIM)
    tbl = pa.Table.from_pydict({"vector": fsl})

    dataset = lance.write_dataset(tbl, tmp_path)
    row = dataset._take_rows([1])
    assert row["vector"]

    ds = dataset.create_index(
        "vector",
        index_type="IVF_PQ",
        num_partitions=2,
        num_sub_vectors=2,
        replace=True,
    )
    tbl = ds.to_table(
        nearest={"column": "vector", "q": data[0:DIM], "k": TOTAL, "nprobes": 2},
        with_row_id=True,
    )
    assert len(tbl) == TOTAL - 1
    assert 1 not in tbl["_rowid"].to_numpy(), "Row with ID 1 is not in the index"


def test_validate_vector_index(tmp_path: Path):
    # make sure the sanity check is correctly catchting issues
    ds = lance.write_dataset(create_table(), tmp_path)
    validate_vector_index(ds, "vector", sample_size=100)

    called = False

    def direct_first_call_to_new_table(*args, **kwargs):
        nonlocal called
        if called:
            return ds.to_table(*args, **kwargs)
        called = True
        return create_table()

    # return a new random table so things fail
    ds.sample = direct_first_call_to_new_table
    with pytest.raises(ValueError, match="Vector index failed sanity check"):
        validate_vector_index(ds, "vector", sample_size=100)


def test_dynamic_projection_with_vectors_index(tmp_path: Path):
    ds = lance.write_dataset(create_table(), tmp_path)
    ds = ds.create_index(
        "vector", index_type="IVF_PQ", num_partitions=4, num_sub_vectors=16
    )

    res = ds.to_table(
        nearest={
            "column": "vector",
            "q": np.random.randn(128),
        },
        columns={
            "vec": "vector",
            "vec_f16": "_cast_list_f16(vector)",
        },
    )

    # TODO: _distance shouldn't be returned by default
    assert res.column_names == ["vec", "vec_f16", "_distance"]

    original = np.stack(res["vec"].to_numpy())
    casted = np.stack(res["vec_f16"].to_numpy())

    assert (original.astype(np.float16) == casted).all()


def test_index_cast_centroids(tmp_path):
    torch = pytest.importorskip("torch")

    tbl = create_table(nvec=1000)

    dataset = lance.write_dataset(tbl, tmp_path)
    dataset = dataset.create_index(
        "vector",
        index_type="IVF_PQ",
        num_partitions=4,
        num_sub_vectors=16,
        accelerator=torch.device("cpu"),
    )

    # Get the centroids
    index_name = dataset.list_indices()[0]["name"]
    index_stats = dataset.stats.index_stats(index_name)
    centroids = index_stats["indices"][0]["centroids"]
    values = pa.array([x for arr in centroids for x in arr], pa.float32())
    centroids = pa.FixedSizeListArray.from_arrays(values, 128)

    dataset.alter_columns(dict(path="vector", data_type=pa.list_(pa.float16(), 128)))

    # centroids are f32, but the column is now f16
    dataset = dataset.create_index(
        "vector",
        index_type="IVF_PQ",
        num_partitions=4,
        num_sub_vectors=16,
        accelerator=torch.device("cpu"),
        ivf_centroids=centroids,
    )


def test_fragment_scan_disallowed_on_ann(dataset):
    q = np.random.randn(128)
    with pytest.raises(
        ValueError, match="This operation is not supported for fragment scan"
    ):
        scanner = dataset.scanner(
            columns=["id"],
            nearest={
                "column": "vector",
                "q": q,
            },
            fragments=[LanceFragment(dataset, 0)],
        )
        scanner.explain_plan(True)


def test_fragment_scan_allowed_on_ann_with_file_scan_prefilter(dataset):
    q = np.random.randn(128)
    scanner = dataset.scanner(
        prefilter=True,
        filter="id>0",
        columns=["id"],
        nearest={
            "column": "vector",
            "q": q,
        },
        fragments=[LanceFragment(dataset, 0)],
    )
    scanner.explain_plan(True)


def test_fragment_scan_disallowed_on_ann_with_index_scan_prefilter(tmp_path):
    tbl = create_table()
    dataset = lance.write_dataset(tbl, tmp_path, max_rows_per_file=250)
    dataset.create_index(
        "vector", index_type="IVF_PQ", num_partitions=4, num_sub_vectors=16
    )
    dataset.create_scalar_index("id", index_type="BTREE")

    assert len(dataset.get_fragments()) == 4

    q = np.random.randn(128)
    results = dataset.scanner(
        prefilter=True,
        filter="id > 50",
        columns=["id"],
        nearest={"column": "vector", "q": q, "use_index": True},
        fragments=[dataset.get_fragment(1)],
    ).to_table()

    results_no_scalar_index = dataset.scanner(
        prefilter=True,
        filter="id > 50",
        columns=["id"],
        nearest={"column": "vector", "q": q, "use_index": True},
        fragments=[dataset.get_fragment(1)],
        use_scalar_index=False,
    ).to_table()

    assert results == results_no_scalar_index


def test_load_indices(dataset):
    indices = dataset.list_indices()
    assert len(indices) == 0

    dataset.create_index(
        "vector", index_type="IVF_PQ", num_partitions=4, num_sub_vectors=16
    )
    indices = dataset.list_indices()
    assert len(indices) == 1


def test_optimize_indices(indexed_dataset):
    data = create_table()
    indexed_dataset = lance.write_dataset(data, indexed_dataset.uri, mode="append")
    indices = indexed_dataset.list_indices()
    assert len(indices) == 1
    indexed_dataset.optimize.optimize_indices(num_indices_to_merge=0)
    indices = indexed_dataset.list_indices()
    assert len(indices) == 2


def test_retrain_indices(indexed_dataset):
    data = create_table()
    indexed_dataset = lance.write_dataset(data, indexed_dataset.uri, mode="append")
    indices = indexed_dataset.list_indices()
    assert len(indices) == 1

    indexed_dataset.optimize.optimize_indices(num_indices_to_merge=0)
    indices = indexed_dataset.list_indices()
    assert len(indices) == 2

    stats = indexed_dataset.stats.index_stats("vector_idx")
    centroids = stats["indices"][0]["centroids"]
    delta_centroids = stats["indices"][1]["centroids"]
    assert centroids == delta_centroids

    indexed_dataset.optimize.optimize_indices(retrain=True)
    new_centroids = indexed_dataset.stats.index_stats("vector_idx")["indices"][0][
        "centroids"
    ]
    indices = indexed_dataset.list_indices()
    assert len(indices) == 1
    assert centroids != new_centroids


def test_no_include_deleted_rows(indexed_dataset):
    with pytest.raises(ValueError, match="Cannot include deleted rows"):
        indexed_dataset.to_table(
            nearest={
                "column": "vector",
                "q": np.random.randn(128),
                "k": 10,
            },
            with_row_id=True,
            include_deleted_rows=True,
        )


def test_drop_indices(indexed_dataset):
    idx_name = indexed_dataset.list_indices()[0]["name"]

    indexed_dataset.drop_index(idx_name)
    indices = indexed_dataset.list_indices()
    assert len(indices) == 0

    test_vec = (
        indexed_dataset.take([0], columns=["vector"]).column("vector").to_pylist()[0]
    )

    # make sure we can still search the column (will do flat search)
    results = indexed_dataset.to_table(
        nearest={
            "column": "vector",
            "q": test_vec,
            "k": 15,
            "nprobes": 1,
        },
    )

    assert len(results) == 15


def test_read_partition(indexed_dataset):
    idx_name = indexed_dataset.list_indices()[0]["name"]
    reader = VectorIndexReader(indexed_dataset, idx_name)

    num_rows = indexed_dataset.count_rows()
    row_sum = 0
    for part_id in range(reader.num_partitions()):
        res = reader.read_partition(part_id)
        row_sum += res.num_rows
        assert "_rowid" in res.column_names
    assert row_sum == num_rows

    row_sum = 0
    for part_id in range(reader.num_partitions()):
        res = reader.read_partition(part_id, with_vector=True)
        row_sum += res.num_rows
        pq_column = res["__pq_code"]
        assert "_rowid" in res.column_names
        assert pq_column.type == pa.list_(pa.uint8(), 16)
    assert row_sum == num_rows

    # error tests
    with pytest.raises(IndexError, match="out of range"):
        reader.read_partition(reader.num_partitions() + 1)

    with pytest.raises(ValueError, match="not vector index"):
        indexed_dataset.create_scalar_index("id", index_type="BTREE")
        VectorIndexReader(indexed_dataset, "id_idx")


def test_vector_index_with_prefilter_and_scalar_index(indexed_dataset):
    uri = indexed_dataset.uri
    new_table = create_table()
    ds = lance.write_dataset(new_table, uri, mode="append")
    ds.optimize.optimize_indices(num_indices_to_merge=0)
    ds.create_scalar_index("id", index_type="BTREE")

    raw_table = create_table()
    ds = lance.write_dataset(raw_table, uri, mode="append")
    ds.optimize.optimize_indices(num_indices_to_merge=0, index_names=["vector_idx"])

    res = ds.to_table(
        nearest={
            "column": "vector",
            "q": np.random.randn(128),
            "k": 10,
        },
        filter="id > 0",
        with_row_id=True,
        prefilter=True,
    )
    assert len(res) == 10


def test_vector_index_with_nprobes(indexed_dataset):
    res = indexed_dataset.scanner(
        nearest={
            "column": "vector",
            "q": np.random.randn(128),
            "k": 10,
            "nprobes": 7,
        }
    ).explain_plan()

    assert "minimum_nprobes=7" in res
    assert "maximum_nprobes=Some(7)" in res

    res = indexed_dataset.scanner(
        nearest={
            "column": "vector",
            "q": np.random.randn(128),
            "k": 10,
            "minimum_nprobes": 7,
        }
    ).explain_plan()

    assert "minimum_nprobes=7" in res
    assert "maximum_nprobes=None" in res

    res = indexed_dataset.scanner(
        nearest={
            "column": "vector",
            "q": np.random.randn(128),
            "k": 10,
            "minimum_nprobes": 7,
            "maximum_nprobes": 10,
        }
    ).explain_plan()

    assert "minimum_nprobes=7" in res
    assert "maximum_nprobes=Some(10)" in res

    res = indexed_dataset.scanner(
        nearest={
            "column": "vector",
            "q": np.random.randn(128),
            "k": 10,
            "maximum_nprobes": 30,
        }
    ).analyze_plan()

    print(res)
