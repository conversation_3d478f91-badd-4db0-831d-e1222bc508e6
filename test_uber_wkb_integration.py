#!/usr/bin/env python3
"""
Uber Data WKB Integration Testing
Creates WKB Point geometries from Uber pickup/dropoff lat/lon columns and tests with Lance GeoPage.
Based on the comprehensive analysis from UBER_AB_ANALYSIS.md
"""

import os
import sys
import time
import json
import struct
import pandas as pd
import pyarrow as pa
import lance
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Any
import numpy as np

class UberWKBTester:
    def __init__(self, mode: str = "custom"):
        self.mode = mode  # "opensource", "custom", "presorted"
        self.results = {}
        
    def create_wkb_point(self, lon: float, lat: float, big_endian: bool = False) -> bytes:
        """Create WKB Point geometry from lon/lat coordinates."""
        if pd.isna(lon) or pd.isna(lat) or lon == 0 or lat == 0:
            return None
            
        endian = b'\x00' if big_endian else b'\x01'  # 0 = big endian, 1 = little endian
        geom_type = struct.pack('<I' if not big_endian else '>I', 1)  # Point = 1
        x = struct.pack('<d' if not big_endian else '>d', lon)
        y = struct.pack('<d' if not big_endian else '>d', lat)
        return endian + geom_type + x + y

    def load_and_enhance_uber_data(self, file_path: str, max_rows: int = None) -> pd.DataFrame:
        """Load Uber data and create WKB Point geometries from lat/lon columns."""
        print(f"🚗 Loading Uber data from: {file_path}")
        
        if max_rows:
            df = pd.read_parquet(file_path).head(max_rows)
        else:
            df = pd.read_parquet(file_path)
        
        print(f"✅ Loaded {len(df):,} rows, {len(df.columns)} columns")
        
        # Analyze spatial columns
        spatial_cols = ['pickup_longitude', 'pickup_latitude', 'dropoff_longitude', 'dropoff_latitude']
        available_spatial = [col for col in spatial_cols if col in df.columns]
        print(f"🗺️ Found spatial columns: {available_spatial}")
        
        # Create WKB Point geometries for pickup and dropoff locations
        wkb_geometries = []
        
        print("🔧 Creating WKB Point geometries...")
        for i, row in df.iterrows():
            if i % 10000 == 0:
                print(f"  Progress: {i:,}/{len(df):,} ({i/len(df)*100:.1f}%)")
            
            pickup_wkb = None
            dropoff_wkb = None
            
            # Create pickup point WKB
            if 'pickup_longitude' in df.columns and 'pickup_latitude' in df.columns:
                pickup_lon = row['pickup_longitude']
                pickup_lat = row['pickup_latitude']
                if (pd.notna(pickup_lon) and pd.notna(pickup_lat) and 
                    pickup_lon != 0 and pickup_lat != 0 and
                    -180 <= pickup_lon <= 180 and -90 <= pickup_lat <= 90):
                    pickup_wkb = self.create_wkb_point(pickup_lon, pickup_lat)
            
            # Create dropoff point WKB
            if 'dropoff_longitude' in df.columns and 'dropoff_latitude' in df.columns:
                dropoff_lon = row['dropoff_longitude']
                dropoff_lat = row['dropoff_latitude']
                if (pd.notna(dropoff_lon) and pd.notna(dropoff_lat) and 
                    dropoff_lon != 0 and dropoff_lat != 0 and
                    -180 <= dropoff_lon <= 180 and -90 <= dropoff_lat <= 90):
                    dropoff_wkb = self.create_wkb_point(dropoff_lon, dropoff_lat)
            
            wkb_geometries.append({
                'pickup_wkb': pickup_wkb,
                'dropoff_wkb': dropoff_wkb,
            })
        
        # Add WKB columns to dataframe
        df['pickup_point_wkb'] = [g['pickup_wkb'] for g in wkb_geometries]
        df['dropoff_point_wkb'] = [g['dropoff_wkb'] for g in wkb_geometries]
        
        # Count valid geometries
        valid_pickup = df['pickup_point_wkb'].notna().sum()
        valid_dropoff = df['dropoff_point_wkb'].notna().sum()
        
        print(f"✅ Created WKB geometries:")
        print(f"  📍 Valid pickup points: {valid_pickup:,} ({valid_pickup/len(df)*100:.1f}%)")
        print(f"  📍 Valid dropoff points: {valid_dropoff:,} ({valid_dropoff/len(df)*100:.1f}%)")
        
        return df

    def analyze_spatial_bounds(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze spatial bounds of the WKB-enhanced data."""
        spatial_analysis = {}
        
        # Analyze pickup locations
        pickup_mask = df['pickup_point_wkb'].notna()
        if pickup_mask.sum() > 0:
            pickup_data = df[pickup_mask]
            spatial_analysis['pickup_bounds'] = {
                'valid_rows': len(pickup_data),
                'validity_rate': len(pickup_data) / len(df),
                'min_lon': float(pickup_data['pickup_longitude'].min()),
                'max_lon': float(pickup_data['pickup_longitude'].max()),
                'min_lat': float(pickup_data['pickup_latitude'].min()),
                'max_lat': float(pickup_data['pickup_latitude'].max()),
            }
        
        # Analyze dropoff locations
        dropoff_mask = df['dropoff_point_wkb'].notna()
        if dropoff_mask.sum() > 0:
            dropoff_data = df[dropoff_mask]
            spatial_analysis['dropoff_bounds'] = {
                'valid_rows': len(dropoff_data),
                'validity_rate': len(dropoff_data) / len(df),
                'min_lon': float(dropoff_data['dropoff_longitude'].min()),
                'max_lon': float(dropoff_data['dropoff_longitude'].max()),
                'min_lat': float(dropoff_data['dropoff_latitude'].min()),
                'max_lat': float(dropoff_data['dropoff_latitude'].max()),
            }
        
        return spatial_analysis

    def create_wkb_spatial_queries(self, spatial_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create spatial queries that can test WKB geometry filtering."""
        queries = []
        
        if 'pickup_bounds' in spatial_analysis:
            bounds = spatial_analysis['pickup_bounds']
            
            # Manhattan Core area (based on UBER_AB_ANALYSIS.md)
            queries.append({
                'name': 'Manhattan Core WKB',
                'description': 'High-density Manhattan area for WKB testing',
                'filter': (
                    f"pickup_longitude >= -73.99 AND pickup_longitude <= -73.97 AND "
                    f"pickup_latitude >= 40.74 AND pickup_latitude <= 40.78"
                ),
                'expected_selectivity': 0.417  # 41.7% from analysis
            })
            
            # Small selective area (best for GeoPage optimization)
            queries.append({
                'name': 'Small Area WKB',
                'description': 'Small selective area for WKB spatial optimization',
                'filter': (
                    f"pickup_longitude >= -73.985 AND pickup_longitude <= -73.975 AND "
                    f"pickup_latitude >= 40.755 AND pickup_latitude <= 40.765"
                ),
                'expected_selectivity': 0.055  # 5.5% from analysis
            })
            
            # Airport area (very selective)
            queries.append({
                'name': 'Airport Area WKB',
                'description': 'JFK Airport vicinity for WKB testing',
                'filter': (
                    f"dropoff_longitude >= -73.82 AND dropoff_longitude <= -73.76 AND "
                    f"dropoff_latitude >= 40.63 AND dropoff_latitude <= 40.67"
                ),
                'expected_selectivity': 0.006  # 0.6% from analysis
            })
        
        return queries

    def apply_spatial_presorting(self, df: pd.DataFrame) -> pd.DataFrame:
        """Apply Z-order spatial pre-sorting for presorted mode."""
        if self.mode != "presorted":
            return df
            
        print("🔄 Applying Z-order spatial pre-sorting...")
        
        # Filter to valid pickup coordinates for sorting
        valid_mask = df['pickup_point_wkb'].notna()
        valid_df = df[valid_mask].copy()
        invalid_df = df[~valid_mask].copy()
        
        if len(valid_df) == 0:
            print("  ⚠️ No valid WKB geometries found, returning original data")
            return df
        
        # Calculate Morton codes for spatial sorting
        lon_min, lon_max = valid_df['pickup_longitude'].min(), valid_df['pickup_longitude'].max()
        lat_min, lat_max = valid_df['pickup_latitude'].min(), valid_df['pickup_latitude'].max()
        
        lon_range = max(lon_max - lon_min, 1e-10)
        lat_range = max(lat_max - lat_min, 1e-10)
        
        # Normalize to [0, 1]
        norm_lon = (valid_df['pickup_longitude'] - lon_min) / lon_range
        norm_lat = (valid_df['pickup_latitude'] - lat_min) / lat_range
        
        # Calculate Morton codes
        SCALE = 1000000
        int_lon = (norm_lon * SCALE).astype(int)
        int_lat = (norm_lat * SCALE).astype(int)
        
        morton_codes = []
        for lon, lat in zip(int_lon, int_lat):
            morton = 0
            for i in range(20):
                morton |= (lon & (1 << i)) << i
                morton |= (lat & (1 << i)) << (i + 1)
            morton_codes.append(morton)
        
        valid_df['_morton_code'] = morton_codes
        valid_df_sorted = valid_df.sort_values('_morton_code').drop('_morton_code', axis=1)
        
        result_df = pd.concat([valid_df_sorted, invalid_df], ignore_index=True)
        
        print(f"  ✅ Z-order sorted {len(valid_df):,} rows with valid WKB geometries")
        return result_df

    def run_wkb_performance_test(self, df: pd.DataFrame, spatial_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Run comprehensive WKB performance tests."""
        print(f"\n🧪 Running WKB performance tests in {self.mode.upper()} mode...")
        
        # Handle case where df is None (using existing dataset)
        if df is not None:
            results = {
                'mode': self.mode,
                'data_info': {
                    'rows': len(df),
                    'columns': len(df.columns),
                    'pickup_wkb_count': df['pickup_point_wkb'].notna().sum(),
                    'dropoff_wkb_count': df['dropoff_point_wkb'].notna().sum(),
                    'memory_mb': df.memory_usage(deep=True).sum() / (1024 * 1024),
                },
                'spatial_analysis': spatial_analysis,
            }
        else:
            # Approximate data info for existing dataset
            results = {
                'mode': self.mode,
                'data_info': {
                    'rows': 12000000,  # Known from our datasets
                    'columns': 20,     # Approximate
                    'pickup_wkb_count': 11800000,  # Approximate
                    'dropoff_wkb_count': 11800000,  # Approximate
                    'memory_mb': 3200, # Approximate (12M rows * ~270 bytes/row)
                },
                'spatial_analysis': spatial_analysis,
            }
        
        # Create dataset path
        dataset_path = Path(f"datasets/uber_comprehensive_{self.mode}")
        dataset_path.parent.mkdir(exist_ok=True)

        # Check if dataset already exists
        if dataset_path.exists():
            print(f"📁 Found existing dataset: {dataset_path}")
            print("🔄 Skipping write test, using existing dataset for read performance testing...")

            # Open existing dataset
            dataset = lance.dataset(dataset_path)

            # Get dataset size for existing dataset
            dataset_size = sum(f.stat().st_size for f in dataset_path.rglob('*') if f.is_file())
            dataset_size_mb = dataset_size / (1024 * 1024)

            results['write_performance'] = {
                'time_seconds': 0.0,  # No write performed
                'dataset_size_mb': dataset_size_mb,
                'compression_ratio': results['data_info']['memory_mb'] / dataset_size_mb,
                'write_success': True,
                'existing_dataset': True,
            }

            print(f"✅ Using existing dataset: {dataset_size_mb:.2f}MB")

        else:
            # Apply spatial pre-sorting if needed
            df_to_write = self.apply_spatial_presorting(df)

            # Test 1: Write Performance with WKB data
            print("📝 Testing WKB write performance...")
            write_start = time.time()

            try:
                if self.mode == "custom":
                    print("🔧 Using custom Lance with GeoPage encoding for WKB columns only...")
                    # Apply GeoPage encoding only to WKB geometry columns, not all columns
                    # This ensures other columns (IDs, timestamps, etc.) use Lance's standard encoders
                    dataset = lance.write_dataset(df_to_write, dataset_path, encoding="geopage")
                else:
                    print("🔧 Using standard Lance encoding...")
                    dataset = lance.write_dataset(df_to_write, dataset_path)

                write_time = time.time() - write_start

                # Get dataset size
                dataset_size = sum(f.stat().st_size for f in dataset_path.rglob('*') if f.is_file())
                dataset_size_mb = dataset_size / (1024 * 1024)

                results['write_performance'] = {
                    'time_seconds': write_time,
                    'dataset_size_mb': dataset_size_mb,
                    'compression_ratio': results['data_info']['memory_mb'] / dataset_size_mb,
                    'write_success': True,
                    'existing_dataset': False,
                }

                print(f"✅ Write successful: {write_time:.3f}s, {dataset_size_mb:.2f}MB")

            except Exception as e:
                results['write_performance'] = {
                    'error': str(e),
                    'write_success': False,
                    'existing_dataset': False,
                }
                print(f"❌ Write failed: {e}")
                return results
        
        # Test 2: WKB Column Access Performance (this is the key 28x performance test!)
        print("🗺️ Testing WKB column access performance...")

        try:
            wkb_start = time.time()

            # Access WKB columns directly - this is where the 28x performance improvement should show
            pickup_wkb = dataset.to_table(columns=['pickup_point_wkb']).to_pandas()['pickup_point_wkb']
            dropoff_wkb = dataset.to_table(columns=['dropoff_point_wkb']).to_pandas()['dropoff_point_wkb']

            wkb_access_time = time.time() - wkb_start

            pickup_count = pickup_wkb.notna().sum()
            dropoff_count = dropoff_wkb.notna().sum()

            results['wkb_column_performance'] = {
                'time_seconds': wkb_access_time,
                'pickup_wkb_count': int(pickup_count),
                'dropoff_wkb_count': int(dropoff_count),
                'wkb_success': True,
                'skipped': False,
            }

            print(f"✅ WKB access successful: {wkb_access_time:.3f}s")
            print(f"📍 Pickup WKB: {pickup_count:,} geometries")
            print(f"📍 Dropoff WKB: {dropoff_count:,} geometries")

        except Exception as e:
            print(f"❌ WKB access failed: {e}")
            results['wkb_column_performance'] = {
                'time_seconds': 0.0,
                'pickup_wkb_count': 0,
                'dropoff_wkb_count': 0,
                'wkb_success': False,
                'skipped': False,
                'error': str(e)
            }
        
        # Test 3: Spatial Query Performance (with WKB data present)
        print("🔍 Testing spatial queries with WKB data...")
        spatial_queries = self.create_wkb_spatial_queries(spatial_analysis)
        query_results = []
        
        for query in spatial_queries:
            query_start = time.time()
            try:
                filtered_data = dataset.to_table(filter=query['filter']).to_pandas()
                query_time = time.time() - query_start
                
                # Calculate selectivity based on total rows
                total_rows = results['data_info']['rows']

                query_result = {
                    'name': query['name'],
                    'description': query['description'],
                    'filter': query['filter'],
                    'time_seconds': query_time,
                    'rows_returned': len(filtered_data),
                    'selectivity': len(filtered_data) / total_rows,
                    'wkb_geometries_returned': filtered_data['pickup_point_wkb'].notna().sum(),
                }
                
                query_results.append(query_result)
                print(f"  ✅ {query['name']}: {query_time:.3f}s, {len(filtered_data):,} rows, {query_result['wkb_geometries_returned']:,} WKB")
                
            except Exception as e:
                print(f"  ❌ {query['name']}: Error - {e}")
                query_results.append({
                    'name': query['name'],
                    'error': str(e)
                })
        
        results['spatial_query_performance'] = query_results

        return results

    def save_results(self, results: Dict[str, Any], output_dir: str = "test_results"):
        """Save test results to JSON file."""
        os.makedirs(output_dir, exist_ok=True)
        timestamp = int(time.time())
        filename = f"uber_wkb_results_{self.mode}_{timestamp}.json"
        filepath = Path(output_dir) / filename

        with open(filepath, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        print(f"💾 Results saved to: {filepath}")
        return filepath

def main():
    import argparse

    parser = argparse.ArgumentParser(description="Uber data WKB integration testing")
    parser.add_argument("--mode", choices=["opensource", "custom", "presorted"], default="custom",
                       help="Testing mode: opensource, custom Lance with GeoPage, or presorted")
    parser.add_argument("--data-file", type=str,
                       default="uber_data/nyc-taxi/2009/01/data.parquet",
                       help="Path to Uber data parquet file")
    parser.add_argument("--max-rows", type=int, default=100000,
                       help="Maximum rows to test (for quick testing)")
    parser.add_argument("--use-existing", action='store_true',
                       help="Use existing dataset if available (skip data loading and writing)")

    args = parser.parse_args()

    print("=" * 80)
    print(f"🚗 UBER DATA WKB INTEGRATION TESTING - {args.mode.upper()} MODE")
    print("=" * 80)

    # Initialize tester
    tester = UberWKBTester(mode=args.mode)

    # Check if we should use existing dataset
    dataset_path = Path(f"datasets/uber_comprehensive_{args.mode}")

    if args.use_existing and dataset_path.exists():
        print(f"🔄 Using existing dataset: {dataset_path}")
        print("⚡ Skipping data loading and enhancement - running read-only performance tests...")

        # Create minimal spatial analysis for existing dataset
        spatial_analysis = {
            'pickup_bounds': {'valid_rows': 12000000, 'validity_rate': 0.98},  # Approximate
            'dropoff_bounds': {'valid_rows': 12000000, 'validity_rate': 0.98}  # Approximate
        }

        # Run performance tests with None for df (won't be used for existing datasets)
        results = tester.run_wkb_performance_test(None, spatial_analysis)

    else:
        # Load and enhance data with WKB geometries
        if not os.path.exists(args.data_file):
            print(f"❌ Data file not found: {args.data_file}")
            sys.exit(1)

        df = tester.load_and_enhance_uber_data(args.data_file, max_rows=args.max_rows)

        # Analyze spatial properties
        print("\n🗺️ Analyzing spatial properties...")
        spatial_analysis = tester.analyze_spatial_bounds(df)

        for location, bounds in spatial_analysis.items():
            print(f"✅ {location}: {bounds['valid_rows']:,} valid geometries ({bounds['validity_rate']:.1%})")

        # Run performance tests
        results = tester.run_wkb_performance_test(df, spatial_analysis)

    # Save results
    results_file = tester.save_results(results)

    # Print summary
    print("\n" + "=" * 80)
    print(f"📊 UBER WKB INTEGRATION SUMMARY ({args.mode.upper()})")
    print("=" * 80)

    if results['write_performance'].get('write_success'):
        write_perf = results['write_performance']
        if write_perf.get('existing_dataset'):
            print(f"📁 Used existing dataset: {write_perf['dataset_size_mb']:.2f}MB")
            print(f"📊 Compression: {write_perf['compression_ratio']:.2f}x")
        else:
            print(f"📝 Write: {write_perf['time_seconds']:.3f}s, {write_perf['dataset_size_mb']:.2f}MB")
            print(f"📊 Compression: {write_perf['compression_ratio']:.2f}x")
    else:
        print("❌ Write failed")

    if results['wkb_column_performance'].get('wkb_success'):
        wkb_perf = results['wkb_column_performance']
        print(f"🗺️ WKB access: {wkb_perf['time_seconds']:.3f}s")
        print(f"📍 Pickup WKB: {wkb_perf['pickup_wkb_count']:,} geometries")
        print(f"📍 Dropoff WKB: {wkb_perf['dropoff_wkb_count']:,} geometries")
    elif results['wkb_column_performance'].get('skipped'):
        print(f"⚠️ WKB access skipped: {results['wkb_column_performance']['reason']}")
    else:
        print("❌ WKB access failed")

    successful_queries = len([q for q in results['spatial_query_performance'] if 'error' not in q])
    print(f"🔍 Spatial queries: {successful_queries}/{len(results['spatial_query_performance'])} successful")

    for query in results['spatial_query_performance']:
        if 'error' not in query:
            print(f"  ✅ {query['name']}: {query['time_seconds']:.3f}s ({query['selectivity']:.1%} selectivity)")

    print(f"\n💾 Detailed results saved to: {results_file}")
    print("🎉 Uber WKB integration testing complete!")

    return results

if __name__ == "__main__":
    results = main()
