#!/usr/bin/env python3
"""
WKB Read Performance Testing - No Dataset Writing
Tests read performance on pre-existing Lance datasets with WKB data.
Focuses on read-side performance comparison between open source and GeoPage.
"""

import os
import sys
import time
import json
import argparse
from pathlib import Path
from typing import Dict, Any, List

def drop_caches():
    """Drop OS page caches for fair benchmarking."""
    try:
        import subprocess
        result = subprocess.run(
            ["sudo", "sh", "-c", "echo 3 > /proc/sys/vm/drop_caches"],
            capture_output=True, text=True, timeout=5
        )
        if result.returncode == 0:
            print("✅ Dropped OS page caches")
            return True
        else:
            print("🟡 Could not drop caches (no sudo)")
            return False
    except:
        print("🟡 Could not drop caches")
        return False

def create_large_wkb_dataset(data_file: str, max_rows: int, mode: str) -> str:
    """Create a large WKB dataset for testing."""
    import pandas as pd
    import lance
    from shapely.geometry import Point
    from shapely import wkb
    
    print(f"📊 Creating {mode} WKB dataset with {max_rows:,} rows...")
    
    # Read Uber data
    df = pd.read_parquet(data_file)
    
    # Repeat data to reach target size
    if len(df) < max_rows:
        repeat_factor = (max_rows // len(df)) + 1
        df = pd.concat([df] * repeat_factor, ignore_index=True)
    
    df = df.head(max_rows)
    
    # Create WKB geometries
    def create_wkb_point(lat, lon):
        if pd.isna(lat) or pd.isna(lon):
            return None
        try:
            if -90 <= lat <= 90 and -180 <= lon <= 180:
                point = Point(lon, lat)
                return point.wkb
            return None
        except:
            return None
    
    print("🗺️ Creating WKB geometries...")
    df['pickup_wkb'] = df.apply(lambda row: create_wkb_point(row['pickuplat'], row['pickuplong']), axis=1)
    df['dropoff_wkb'] = df.apply(lambda row: create_wkb_point(row['dropfflat'], row['dropfflon']), axis=1)
    
    # Keep only essential columns
    columns_to_keep = ['pickup_wkb', 'dropoff_wkb', 'pickuplat', 'pickuplong', 'dropfflat', 'dropfflon']
    df = df[columns_to_keep]
    
    # Set encoding based on mode
    encoding = None
    if mode == 'custom':
        os.environ['LANCE_GEOPAGE_ENCODING_REQUESTED'] = '1'
        encoding = {"pickup_wkb": "geopage", "dropoff_wkb": "geopage"}
    else:
        os.environ.pop('LANCE_GEOPAGE_ENCODING_REQUESTED', None)
    
    # Write dataset
    dataset_path = f"test_results/large_wkb_dataset_{mode}_{max_rows}"
    
    start_time = time.time()
    if encoding:
        lance.write_dataset(df, dataset_path, mode="overwrite", encoding=encoding)
    else:
        lance.write_dataset(df, dataset_path, mode="overwrite")
    write_time = time.time() - start_time
    
    # Get dataset size
    dataset_size = sum(f.stat().st_size for f in Path(dataset_path).rglob('*') if f.is_file())
    dataset_size_mb = dataset_size / (1024 * 1024)
    
    print(f"✅ {mode} dataset created: {write_time:.3f}s, {dataset_size_mb:.2f}MB")
    
    return dataset_path

def test_wkb_read_performance(dataset_path: str, mode: str) -> Dict[str, Any]:
    """Test WKB read performance on existing dataset."""
    import lance

    # Set environment for the mode
    if mode == 'custom':
        os.environ['LANCE_GEOPAGE_ENCODING_REQUESTED'] = '1'
    else:
        os.environ.pop('LANCE_GEOPAGE_ENCODING_REQUESTED', None)
    
    print(f"\n🔍 Testing {mode} WKB read performance...")
    
    results = {
        'mode': mode,
        'dataset_path': dataset_path,
        'timestamp': time.time()
    }
    
    try:
        # Open dataset
        dataset = lance.dataset(dataset_path)
        
        # Test 1: Full WKB column access
        print("📊 Testing full WKB column access...")
        start_time = time.time()
        
        pickup_wkb = dataset.to_table(columns=['pickup_point_wkb']).to_pandas()['pickup_point_wkb']
        dropoff_wkb = dataset.to_table(columns=['dropoff_point_wkb']).to_pandas()['dropoff_point_wkb']
        
        wkb_access_time = time.time() - start_time
        
        pickup_count = pickup_wkb.notna().sum()
        dropoff_count = dropoff_wkb.notna().sum()
        total_rows = len(pickup_wkb)
        
        results['wkb_column_access'] = {
            'time_seconds': wkb_access_time,
            'total_rows': total_rows,
            'pickup_wkb_count': int(pickup_count),
            'dropoff_wkb_count': int(dropoff_count),
            'success': True
        }
        
        print(f"✅ WKB access: {wkb_access_time:.3f}s, {total_rows:,} rows")
        print(f"   📍 Pickup WKB: {pickup_count:,} geometries")
        print(f"   📍 Dropoff WKB: {dropoff_count:,} geometries")
        
        # Test 2: Spatial queries
        print("🗺️ Testing spatial queries...")
        spatial_queries = [
            {
                'name': 'Manhattan Core',
                'filter': "(pickup_latitude >= 40.7500 AND pickup_latitude <= 40.7800) AND (pickup_longitude >= -74.0100 AND pickup_longitude <= -73.9700)"
            },
            {
                'name': 'Small Area',
                'filter': "(pickup_latitude >= 40.7600 AND pickup_latitude <= 40.7650) AND (pickup_longitude >= -73.9900 AND pickup_longitude <= -73.9850)"
            },
            {
                'name': 'Airport Area',
                'filter': "(pickup_latitude >= 40.6400 AND pickup_latitude <= 40.6500) AND (pickup_longitude >= -73.7900 AND pickup_longitude <= -73.7700)"
            }
        ]
        
        spatial_results = []
        for query in spatial_queries:
            try:
                start_time = time.time()
                
                # Execute spatial filter
                filtered_data = dataset.to_table(
                    filter=query['filter'],
                    columns=['pickup_point_wkb', 'dropoff_point_wkb', 'pickup_latitude', 'pickup_longitude']
                ).to_pandas()
                
                query_time = time.time() - start_time
                
                result_count = len(filtered_data)
                pickup_wkb_count = filtered_data['pickup_point_wkb'].notna().sum()
                dropoff_wkb_count = filtered_data['dropoff_point_wkb'].notna().sum()
                
                selectivity = (result_count / total_rows) * 100
                
                spatial_results.append({
                    'name': query['name'],
                    'filter': query['filter'],
                    'time_seconds': query_time,
                    'result_count': result_count,
                    'pickup_wkb_count': int(pickup_wkb_count),
                    'dropoff_wkb_count': int(dropoff_wkb_count),
                    'selectivity_percent': selectivity,
                    'success': True
                })
                
                print(f"  ✅ {query['name']}: {query_time:.3f}s, {result_count:,} rows ({selectivity:.1f}% selectivity)")
                
            except Exception as e:
                print(f"  ❌ {query['name']}: {str(e)}")
                spatial_results.append({
                    'name': query['name'],
                    'error': str(e),
                    'success': False
                })
        
        results['spatial_queries'] = spatial_results
        
        # Test 3: Random access patterns
        print("🎲 Testing random access patterns...")
        try:
            import random
            
            # Random row sampling
            sample_size = min(10000, total_rows // 10)
            random_indices = random.sample(range(total_rows), sample_size)
            
            start_time = time.time()
            
            # Access random rows
            for i in range(0, len(random_indices), 1000):  # Batch access
                batch_indices = random_indices[i:i+1000]
                batch_filter = " OR ".join([f"__index = {idx}" for idx in batch_indices])
                
                batch_data = dataset.to_table(
                    filter=batch_filter,
                    columns=['pickup_point_wkb', 'dropoff_point_wkb']
                ).to_pandas()
            
            random_access_time = time.time() - start_time
            
            results['random_access'] = {
                'time_seconds': random_access_time,
                'sample_size': sample_size,
                'success': True
            }
            
            print(f"✅ Random access: {random_access_time:.3f}s, {sample_size:,} samples")
            
        except Exception as e:
            print(f"❌ Random access failed: {str(e)}")
            results['random_access'] = {
                'error': str(e),
                'success': False
            }
        
        results['overall_success'] = True
        
    except Exception as e:
        print(f"❌ Dataset read failed: {str(e)}")
        results['error'] = str(e)
        results['overall_success'] = False
    
    return results

def compare_read_performance(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Compare read performance across different modes."""
    print(f"\n{'='*80}")
    print("📊 WKB READ PERFORMANCE COMPARISON")
    print(f"{'='*80}")
    
    if len(results) < 2:
        print("❌ Need at least 2 results for comparison")
        return {}
    
    # Find open source and custom results
    opensource_result = None
    custom_result = None
    
    for result in results:
        if result['mode'] == 'opensource':
            opensource_result = result
        elif result['mode'] == 'custom':
            custom_result = result
    
    if not opensource_result or not custom_result:
        print("❌ Need both opensource and custom results for comparison")
        return {}
    
    comparison = {}
    
    # Compare WKB column access
    print("\n🗺️ WKB COLUMN ACCESS COMPARISON")
    print(f"{'Mode':<15} {'Time (s)':<10} {'Total Rows':<12} {'Pickup WKB':<12} {'Dropoff WKB':<12}")
    print("-" * 75)
    
    os_wkb = opensource_result.get('wkb_column_access', {})
    custom_wkb = custom_result.get('wkb_column_access', {})
    
    if os_wkb.get('success') and custom_wkb.get('success'):
        os_time = os_wkb['time_seconds']
        custom_time = custom_wkb['time_seconds']
        improvement = ((os_time - custom_time) / os_time) * 100
        
        print(f"{'opensource':<15} {os_time:<10.3f} {os_wkb['total_rows']:<12,} {os_wkb['pickup_wkb_count']:<12,} {os_wkb['dropoff_wkb_count']:<12,}")
        print(f"{'custom':<15} {custom_time:<10.3f} {custom_wkb['total_rows']:<12,} {custom_wkb['pickup_wkb_count']:<12,} {custom_wkb['dropoff_wkb_count']:<12,}")
        print(f"\n🎯 GeoPage WKB Access: {improvement:+.1f}% ({'faster' if improvement > 0 else 'slower'})")
        
        comparison['wkb_access_improvement'] = improvement
    
    # Compare spatial queries
    print(f"\n🔍 SPATIAL QUERY COMPARISON")
    print(f"{'Query':<20} {'Open Source':<12} {'GeoPage':<12} {'Improvement':<12} {'Selectivity':<12}")
    print("-" * 80)
    
    os_spatial = opensource_result.get('spatial_queries', [])
    custom_spatial = custom_result.get('spatial_queries', [])
    
    spatial_improvements = []
    
    for os_query in os_spatial:
        if not os_query.get('success'):
            continue
            
        # Find matching custom query
        custom_query = None
        for cq in custom_spatial:
            if cq.get('name') == os_query.get('name') and cq.get('success'):
                custom_query = cq
                break
        
        if custom_query:
            os_time = os_query['time_seconds']
            custom_time = custom_query['time_seconds']
            improvement = ((os_time - custom_time) / os_time) * 100
            selectivity = os_query.get('selectivity_percent', 0)
            
            spatial_improvements.append(improvement)
            
            print(f"{os_query['name']:<20} {os_time:<12.3f} {custom_time:<12.3f} {improvement:+.1f}%{'':<7} {selectivity:<12.1f}%")
    
    if spatial_improvements:
        avg_improvement = sum(spatial_improvements) / len(spatial_improvements)
        comparison['spatial_query_avg_improvement'] = avg_improvement
        print(f"\n🎯 Average Spatial Query Improvement: {avg_improvement:+.1f}%")
    
    return comparison

def main():
    parser = argparse.ArgumentParser(description="WKB Read Performance Testing")
    parser.add_argument("--data-file", type=str,
                       default="uber_data/nyc-taxi/2009/01/data.parquet",
                       help="Path to Uber data parquet file")
    parser.add_argument("--max-rows", type=int, default=1000000,
                       help="Maximum rows for dataset creation")
    parser.add_argument("--modes", nargs='+', 
                       choices=['opensource', 'custom', 'both'],
                       default=['both'],
                       help="Test modes to run")
    parser.add_argument("--create-datasets", action='store_true',
                       help="Create new datasets (otherwise use existing)")
    parser.add_argument("--existing-datasets", nargs='+',
                       help="Paths to existing datasets to test")
    
    args = parser.parse_args()
    
    print("🚗" * 20)
    print("🔍 WKB READ PERFORMANCE TESTING")
    print("🚗" * 20)
    
    results = []
    
    if args.existing_datasets:
        # Test existing datasets
        for dataset_path in args.existing_datasets:
            if not os.path.exists(dataset_path):
                print(f"❌ Dataset not found: {dataset_path}")
                continue
                
            # Determine mode from path
            mode = 'custom' if 'custom' in dataset_path else 'opensource'
            
            drop_caches()
            result = test_wkb_read_performance(dataset_path, mode)
            results.append(result)
    
    elif args.create_datasets:
        # Create and test new datasets
        modes_to_test = ['opensource', 'custom'] if 'both' in args.modes else args.modes
        
        for mode in modes_to_test:
            dataset_path = create_large_wkb_dataset(args.data_file, args.max_rows, mode)
            
            drop_caches()
            result = test_wkb_read_performance(dataset_path, mode)
            results.append(result)
    
    else:
        print("❌ Must specify either --existing-datasets or --create-datasets")
        sys.exit(1)
    
    # Compare results
    if len(results) >= 2:
        comparison = compare_read_performance(results)
    
    # Save results
    output_dir = Path("test_results")
    output_dir.mkdir(exist_ok=True)
    
    timestamp = int(time.time())
    results_file = output_dir / f"wkb_read_performance_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump({
            'timestamp': timestamp,
            'test_type': 'wkb_read_performance',
            'results': results,
            'comparison': comparison if len(results) >= 2 else {}
        }, f, indent=2, default=str)
    
    print(f"\n💾 Results saved to: {results_file}")
    print("🎉 WKB read performance testing complete!")

if __name__ == "__main__":
    main()
