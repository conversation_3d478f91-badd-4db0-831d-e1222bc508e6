#!/usr/bin/env python3
"""
Test script to validate GeoPage spatial pushdown implementation with debug logging.
"""

import os
import lance
import pandas as pd
import tempfile

def test_spatial_pushdown_with_logging():
    """Test spatial pushdown with debug logging enabled."""
    
    # Enable Rust debug logging to see our println! statements
    os.environ['LANCE_LOG'] = 'debug'
    os.environ['LANCE_LOG_STYLE'] = 'always'
    
    print("🧪 TESTING GEOPAGE SPATIAL PUSHDOWN WITH DEBUG LOGGING")
    print("=" * 60)
    
    # Create larger test data to trigger spatial filtering
    print("📊 Creating test dataset...")
    data = {
        'pickup_longitude': [-74.0, -73.9, -74.1, -73.8, -74.2, -73.7] * 2000,
        'pickup_latitude': [40.7, 40.8, 40.6, 40.9, 40.5, 41.0] * 2000,
        'trip_id': list(range(12000)),
        'fare': [10.5, 15.2, 8.7, 12.3, 9.8, 14.1] * 2000
    }
    df = pd.DataFrame(data)
    print(f"✅ Created dataset with {len(df):,} rows")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        dataset_path = os.path.join(temp_dir, 'test_spatial_pushdown.lance')
        
        print("\n🔧 Writing dataset with GeoPage encoding...")
        dataset = lance.write_dataset(df, dataset_path, encoding='geopage')
        print("✅ Dataset written successfully")
        
        print("\n🗺️ Testing spatial queries with pushdown...")
        print("Looking for debug output from GeoPageFieldScheduler...")
        print("-" * 50)
        
        # Test 1: Spatial query that should trigger pushdown
        print("\n🎯 Test 1: Manhattan Core area query")
        result1 = dataset.to_table(filter='''
            pickup_longitude >= -74.0 AND pickup_longitude <= -73.9 AND 
            pickup_latitude >= 40.7 AND pickup_latitude <= 40.8
        ''')
        print(f"✅ Query result: {len(result1)} rows")
        
        # Test 2: Smaller spatial query
        print("\n🎯 Test 2: Small area query")
        result2 = dataset.to_table(filter='''
            pickup_longitude >= -73.95 AND pickup_longitude <= -73.85 AND 
            pickup_latitude >= 40.75 AND pickup_latitude <= 40.85
        ''')
        print(f"✅ Query result: {len(result2)} rows")
        
        # Test 3: Non-spatial query (should not trigger spatial pushdown)
        print("\n🎯 Test 3: Non-spatial query (fare filter)")
        result3 = dataset.to_table(filter='fare > 12.0')
        print(f"✅ Query result: {len(result3)} rows")
        
        # Test 4: Full scan (no filter)
        print("\n🎯 Test 4: Full scan (no filter)")
        result4 = dataset.to_table()
        print(f"✅ Query result: {len(result4)} rows")
        
    print("\n" + "=" * 60)
    print("🎉 SPATIAL PUSHDOWN TEST COMPLETED")
    print("Look for debug messages above showing:")
    print("  - 'GeoPageFieldScheduler: Analyzing spatial filter...'")
    print("  - 'Detected spatial filter: bbox(...)'")
    print("  - 'Spatial pushdown: X pages → Y pages'")
    print("  - 'GeoPageScheduler: Creating decoder for N ranges'")

if __name__ == "__main__":
    test_spatial_pushdown_with_logging()
