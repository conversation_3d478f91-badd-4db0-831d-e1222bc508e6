#!/usr/bin/env python3
"""
WKB Integrity Testing
Compare WKB data between open source and GeoPage Lance datasets.
"""

import lance
import pandas as pd
import duckdb
import struct
import time

def analyze_wkb_data(dataset_path: str, dataset_name: str, sample_size: int = 1000):
    """Analyze WKB data integrity in a Lance dataset."""
    print(f"\n🔍 Analyzing WKB data in {dataset_name}")
    print(f"📁 Dataset: {dataset_path}")
    
    # Open dataset
    dataset = lance.dataset(dataset_path)
    
    # Get sample data
    sample = dataset.to_table(limit=sample_size).to_pandas()
    
    print(f"📊 Sample size: {len(sample):,} rows")
    
    # Analyze pickup WKB
    pickup_wkb = sample['pickup_point_wkb'].dropna()
    dropoff_wkb = sample['dropoff_point_wkb'].dropna()
    
    print(f"📍 Pickup WKB: {len(pickup_wkb):,} valid geometries")
    print(f"📍 Dropoff WKB: {len(dropoff_wkb):,} valid geometries")
    
    if len(pickup_wkb) > 0:
        # Analyze WKB structure
        first_wkb = pickup_wkb.iloc[0]
        
        print(f"📏 WKB length: {len(first_wkb)} bytes")
        print(f"🔢 First WKB hex: {first_wkb[:20].hex()}")
        
        # Parse WKB structure
        if len(first_wkb) >= 21:
            byte_order = first_wkb[0]
            wkb_type = struct.unpack('<I' if byte_order == 1 else '>I', first_wkb[1:5])[0]
            
            print(f"📐 Byte order: {byte_order} ({'Little' if byte_order == 1 else 'Big'} endian)")
            print(f"🎯 WKB type: {wkb_type} ({'Point' if wkb_type == 1 else 'Other'})")
            
            if wkb_type == 1:  # Point
                # Extract coordinates
                if byte_order == 1:  # Little endian
                    x = struct.unpack('<d', first_wkb[5:13])[0]
                    y = struct.unpack('<d', first_wkb[13:21])[0]
                else:  # Big endian
                    x = struct.unpack('>d', first_wkb[5:13])[0]
                    y = struct.unpack('>d', first_wkb[13:21])[0]
                
                print(f"📍 Coordinates: ({x:.6f}, {y:.6f})")
                
                # Compare with lat/lon columns
                first_row = sample.iloc[0]
                expected_x = first_row['pickup_longitude']
                expected_y = first_row['pickup_latitude']
                
                print(f"📍 Expected: ({expected_x:.6f}, {expected_y:.6f})")
                print(f"✅ Coordinates match: {abs(x - expected_x) < 1e-10 and abs(y - expected_y) < 1e-10}")
        
        # Check WKB length distribution
        lengths = pickup_wkb.apply(len)
        print(f"📏 WKB lengths - min: {lengths.min()}, max: {lengths.max()}, unique: {lengths.nunique()}")
        
        return {
            'dataset_name': dataset_name,
            'sample_size': len(sample),
            'pickup_wkb_count': len(pickup_wkb),
            'dropoff_wkb_count': len(dropoff_wkb),
            'wkb_length': len(first_wkb) if len(pickup_wkb) > 0 else 0,
            'first_wkb_hex': first_wkb[:20].hex() if len(pickup_wkb) > 0 else '',
            'wkb_type': wkb_type if len(pickup_wkb) > 0 and len(first_wkb) >= 21 else 0,
            'coordinates': (x, y) if len(pickup_wkb) > 0 and len(first_wkb) >= 21 and wkb_type == 1 else None,
            'length_distribution': lengths.value_counts().to_dict() if len(pickup_wkb) > 0 else {}
        }
    
    return {
        'dataset_name': dataset_name,
        'sample_size': len(sample),
        'pickup_wkb_count': 0,
        'dropoff_wkb_count': 0,
        'error': 'No valid WKB data found'
    }

def test_duckdb_wkb_conversion(dataset_path: str, dataset_name: str, sample_size: int = 100):
    """Test WKB conversion in DuckDB."""
    print(f"\n🦆 Testing DuckDB WKB conversion for {dataset_name}")
    
    # Setup DuckDB
    conn = duckdb.connect()
    conn.install_extension('spatial')
    conn.load_extension('spatial')
    
    # Open dataset and register in DuckDB
    dataset = lance.dataset(dataset_path)
    sample_data = dataset.to_table(limit=sample_size).to_pandas()
    conn.register("test_dataset", sample_data)
    
    try:
        # Test basic WKB access
        result = conn.execute(f"SELECT COUNT(*) FROM test_dataset WHERE pickup_point_wkb IS NOT NULL LIMIT {sample_size}").fetchone()[0]
        print(f"✅ Non-null WKB count: {result:,}")
        
        # Test WKB length validation
        length_result = conn.execute(f"SELECT COUNT(*) FROM test_dataset WHERE octet_length(pickup_point_wkb) = 21 LIMIT {sample_size}").fetchone()[0]
        print(f"✅ Valid length WKB count: {length_result:,}")
        
        # Test WKB to geometry conversion
        try:
            geom_result = conn.execute(f"SELECT COUNT(*) FROM test_dataset WHERE pickup_point_wkb IS NOT NULL AND ST_GeomFromWKB(pickup_point_wkb) IS NOT NULL LIMIT {sample_size}").fetchone()[0]
            print(f"✅ Valid geometry conversion count: {geom_result:,}")
        except Exception as e:
            print(f"❌ Geometry conversion failed: {e}")
            geom_result = 0
        
        # Test spatial intersection
        try:
            spatial_result = conn.execute(f"""
                SELECT COUNT(*) FROM test_dataset 
                WHERE pickup_point_wkb IS NOT NULL 
                AND octet_length(pickup_point_wkb) = 21 
                AND ST_Intersects(
                    ST_GeomFromWKB(pickup_point_wkb),
                    ST_MakeEnvelope(-74.01, 40.74, -73.97, 40.78)
                )
                LIMIT {sample_size}
            """).fetchone()[0]
            print(f"✅ Spatial intersection count: {spatial_result:,}")
        except Exception as e:
            print(f"❌ Spatial intersection failed: {e}")
            spatial_result = 0
        
        return {
            'dataset_name': dataset_name,
            'non_null_wkb': result,
            'valid_length_wkb': length_result,
            'valid_geometry_conversion': geom_result,
            'spatial_intersection': spatial_result,
            'success': True
        }
        
    except Exception as e:
        print(f"❌ DuckDB test failed: {e}")
        return {
            'dataset_name': dataset_name,
            'error': str(e),
            'success': False
        }
    finally:
        conn.close()

def compare_wkb_datasets():
    """Compare WKB data between open source and GeoPage datasets."""
    print("🔍 WKB INTEGRITY COMPARISON")
    print("=" * 60)
    
    # Analyze both datasets
    opensource_analysis = analyze_wkb_data("datasets/uber_wkb_opensource", "Open Source", 1000)
    geopage_analysis = analyze_wkb_data("datasets/uber_wkb_custom", "GeoPage", 1000)
    
    # Test DuckDB conversion
    opensource_duckdb = test_duckdb_wkb_conversion("datasets/uber_wkb_opensource", "Open Source", 1000)
    geopage_duckdb = test_duckdb_wkb_conversion("datasets/uber_wkb_custom", "GeoPage", 1000)
    
    # Compare results
    print(f"\n{'='*60}")
    print("📊 WKB INTEGRITY COMPARISON SUMMARY")
    print(f"{'='*60}")
    
    print(f"\n🗺️ WKB Data Analysis:")
    print(f"{'Metric':<25} {'Open Source':<15} {'GeoPage':<15} {'Match':<10}")
    print("-" * 70)
    
    if 'error' not in opensource_analysis and 'error' not in geopage_analysis:
        metrics = [
            ('WKB Count', 'pickup_wkb_count'),
            ('WKB Length', 'wkb_length'),
            ('WKB Type', 'wkb_type'),
        ]
        
        for metric_name, key in metrics:
            os_val = opensource_analysis.get(key, 'N/A')
            gp_val = geopage_analysis.get(key, 'N/A')
            match = "✅" if os_val == gp_val else "❌"
            print(f"{metric_name:<25} {str(os_val):<15} {str(gp_val):<15} {match:<10}")
        
        # Compare coordinates
        os_coords = opensource_analysis.get('coordinates')
        gp_coords = geopage_analysis.get('coordinates')
        if os_coords and gp_coords:
            coords_match = abs(os_coords[0] - gp_coords[0]) < 1e-10 and abs(os_coords[1] - gp_coords[1]) < 1e-10
            print(f"{'Coordinates':<25} {str(os_coords):<15} {str(gp_coords):<15} {'✅' if coords_match else '❌':<10}")
    
    print(f"\n🦆 DuckDB Conversion Analysis:")
    print(f"{'Metric':<25} {'Open Source':<15} {'GeoPage':<15} {'Match':<10}")
    print("-" * 70)
    
    if opensource_duckdb.get('success') and geopage_duckdb.get('success'):
        duckdb_metrics = [
            ('Non-null WKB', 'non_null_wkb'),
            ('Valid Length WKB', 'valid_length_wkb'),
            ('Geometry Conversion', 'valid_geometry_conversion'),
            ('Spatial Intersection', 'spatial_intersection'),
        ]
        
        for metric_name, key in duckdb_metrics:
            os_val = opensource_duckdb.get(key, 'N/A')
            gp_val = geopage_duckdb.get(key, 'N/A')
            match = "✅" if os_val == gp_val else "❌"
            print(f"{metric_name:<25} {str(os_val):<15} {str(gp_val):<15} {match:<10}")
    
    # Identify the issue
    print(f"\n🔍 ISSUE ANALYSIS:")
    if geopage_duckdb.get('spatial_intersection', 0) == 0 and opensource_duckdb.get('spatial_intersection', 0) > 0:
        print("❌ ISSUE FOUND: GeoPage WKB data is not working with DuckDB spatial functions")
        print("   - WKB data exists but ST_GeomFromWKB() or ST_Intersects() fails")
        print("   - This explains why spatial queries return 0 rows")
        
        if geopage_duckdb.get('valid_geometry_conversion', 0) == 0:
            print("   - Root cause: ST_GeomFromWKB() conversion fails")
        else:
            print("   - Root cause: ST_Intersects() spatial operation fails")
    else:
        print("✅ No obvious WKB integrity issues found")
    
    return {
        'opensource_analysis': opensource_analysis,
        'geopage_analysis': geopage_analysis,
        'opensource_duckdb': opensource_duckdb,
        'geopage_duckdb': geopage_duckdb
    }

if __name__ == "__main__":
    results = compare_wkb_datasets()
    print("\n🎉 WKB integrity analysis complete!")
