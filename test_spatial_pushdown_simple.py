#!/usr/bin/env python3
"""
Simple test to validate the enhanced spatial pushdown implementation.
This test creates a small dataset and verifies that spatial filtering works correctly.
"""

import os
import sys
import tempfile
import shutil
import pandas as pd
import numpy as np

# Add the lance-internal python path
sys.path.insert(0, 'lance-internal/python')

try:
    import lance
    print("✅ Lance imported successfully")
except ImportError as e:
    print(f"❌ Failed to import lance: {e}")
    sys.exit(1)

def create_test_dataset():
    """Create a small test dataset with spatial data"""
    print("📊 Creating test dataset...")
    
    # Create a grid of points in NYC area
    lons = np.linspace(-74.1, -73.9, 100)  # NYC longitude range
    lats = np.linspace(40.7, 40.8, 100)    # NYC latitude range
    
    data = []
    for i, lon in enumerate(lons):
        for j, lat in enumerate(lats):
            data.append({
                'id': i * 100 + j,
                'pickup_longitude': lon,
                'pickup_latitude': lat,
                'value': np.random.randint(1, 100)
            })
    
    df = pd.DataFrame(data)
    print(f"📊 Created dataset with {len(df)} rows")
    return df

def test_spatial_pushdown():
    """Test the spatial pushdown functionality"""
    print("🧪 Testing spatial pushdown implementation...")
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        dataset_path = os.path.join(temp_dir, "test_spatial")
        
        # Create test dataset
        df = create_test_dataset()
        
        # Write dataset with GeoPage encoding
        print("💾 Writing dataset with GeoPage encoding...")
        try:
            dataset = lance.write_dataset(
                df,
                dataset_path,
                mode="create"
            )
            print(f"✅ Dataset written successfully to {dataset_path}")
        except Exception as e:
            print(f"❌ Failed to write dataset: {e}")
            return False
        
        # Test spatial query
        print("🔍 Testing spatial query...")
        try:
            # Query a small area in the center
            query_filter = "pickup_longitude >= -74.0 AND pickup_longitude <= -73.95 AND pickup_latitude >= 40.74 AND pickup_latitude <= 40.76"
            
            result = dataset.to_table(filter=query_filter)
            print(f"✅ Spatial query executed successfully")
            print(f"📊 Query returned {len(result)} rows")
            
            # Verify results are within the expected range
            if len(result) > 0:
                result_df = result.to_pandas()
                lon_min, lon_max = result_df['pickup_longitude'].min(), result_df['pickup_longitude'].max()
                lat_min, lat_max = result_df['pickup_latitude'].min(), result_df['pickup_latitude'].max()
                
                print(f"📍 Result longitude range: {lon_min:.3f} to {lon_max:.3f}")
                print(f"📍 Result latitude range: {lat_min:.3f} to {lat_max:.3f}")
                
                # Verify results are within query bounds
                if lon_min >= -74.0 and lon_max <= -73.95 and lat_min >= 40.74 and lat_max <= 40.76:
                    print("✅ Spatial filtering working correctly")
                    return True
                else:
                    print("❌ Spatial filtering returned out-of-bounds results")
                    return False
            else:
                print("⚠️ Query returned no results (this might be expected)")
                return True
                
        except Exception as e:
            print(f"❌ Spatial query failed: {e}")
            return False

def main():
    """Main test function"""
    print("🚀 Starting spatial pushdown test...")
    
    success = test_spatial_pushdown()
    
    if success:
        print("🎉 Spatial pushdown test completed successfully!")
        return 0
    else:
        print("💥 Spatial pushdown test failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
