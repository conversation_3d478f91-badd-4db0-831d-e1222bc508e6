#!/usr/bin/env python3
"""
Test WKB Fix - Create new datasets with fixed GeoPage encoder
"""

import os
import lance
import pandas as pd
import struct
import time

def create_wkb_point(lat: float, lon: float) -> bytes:
    """Create a WKB Point geometry from lat/lon coordinates."""
    # WKB Point format: 
    # - 1 byte: byte order (1 = little endian)
    # - 4 bytes: geometry type (1 = Point)
    # - 8 bytes: X coordinate (longitude)
    # - 8 bytes: Y coordinate (latitude)
    return struct.pack('<BId d', 1, 1, lon, lat)

def create_test_data(num_rows: int = 1000):
    """Create test data with WKB geometries."""
    print(f"🔧 Creating test data with {num_rows:,} rows...")
    
    # Create sample NYC taxi data
    import numpy as np
    np.random.seed(42)
    
    # NYC bounding box
    lat_min, lat_max = 40.7, 40.8
    lon_min, lon_max = -74.0, -73.9
    
    data = {
        'pickup_latitude': np.random.uniform(lat_min, lat_max, num_rows),
        'pickup_longitude': np.random.uniform(lon_min, lon_max, num_rows),
        'dropoff_latitude': np.random.uniform(lat_min, lat_max, num_rows),
        'dropoff_longitude': np.random.uniform(lon_min, lon_max, num_rows),
    }
    
    df = pd.DataFrame(data)
    
    # Create WKB geometries
    print("🗺️ Creating WKB geometries...")
    df['pickup_point_wkb'] = df.apply(lambda row: create_wkb_point(row['pickup_latitude'], row['pickup_longitude']), axis=1)
    df['dropoff_point_wkb'] = df.apply(lambda row: create_wkb_point(row['dropoff_latitude'], row['dropoff_longitude']), axis=1)
    
    print(f"✅ Created {len(df):,} rows with WKB geometries")
    return df

def test_wkb_encoding(mode: str, num_rows: int = 1000):
    """Test WKB encoding with the specified mode."""
    print(f"\n🧪 Testing {mode.upper()} WKB encoding...")
    
    # Create test data
    df = create_test_data(num_rows)
    
    # Set environment for the mode
    if mode == 'custom':
        os.environ['LANCE_GEOPAGE_ENCODING_REQUESTED'] = '1'
        print("🎯 GeoPage encoding enabled")
    else:
        os.environ.pop('LANCE_GEOPAGE_ENCODING_REQUESTED', None)
        print("📊 Standard Lance encoding")
    
    # Create dataset path
    dataset_path = f"test_datasets/wkb_fix_{mode}"
    
    try:
        # Remove existing dataset
        import shutil
        if os.path.exists(dataset_path):
            shutil.rmtree(dataset_path)
        
        os.makedirs(os.path.dirname(dataset_path), exist_ok=True)
        
        # Write dataset
        print(f"📝 Writing dataset to {dataset_path}...")
        start_time = time.time()
        
        if mode == 'custom':
            dataset = lance.write_dataset(df, dataset_path, encoding="geopage")
        else:
            dataset = lance.write_dataset(df, dataset_path)
        
        write_time = time.time() - start_time
        print(f"✅ Write completed in {write_time:.3f}s")
        
        # Test reading
        print("📖 Testing read...")
        start_time = time.time()
        
        # Read a sample
        sample = dataset.to_table(limit=10).to_pandas()
        read_time = time.time() - start_time
        
        print(f"✅ Read completed in {read_time:.3f}s")
        print(f"📊 Sample data shape: {sample.shape}")
        
        # Verify WKB data integrity
        pickup_wkb = sample['pickup_point_wkb'].iloc[0]
        print(f"📏 WKB length: {len(pickup_wkb)} bytes")
        print(f"🔢 WKB hex: {pickup_wkb[:20].hex()}")
        
        # Parse WKB to verify integrity
        if len(pickup_wkb) == 21:
            byte_order = pickup_wkb[0]
            wkb_type = struct.unpack('<I', pickup_wkb[1:5])[0]
            x = struct.unpack('<d', pickup_wkb[5:13])[0]
            y = struct.unpack('<d', pickup_wkb[13:21])[0]
            
            print(f"📐 Byte order: {byte_order} ({'Little' if byte_order == 1 else 'Big'} endian)")
            print(f"🎯 WKB type: {wkb_type} ({'Point' if wkb_type == 1 else 'Other'})")
            print(f"📍 Coordinates: ({x:.6f}, {y:.6f})")
            
            # Compare with expected
            expected_x = sample['pickup_longitude'].iloc[0]
            expected_y = sample['pickup_latitude'].iloc[0]
            print(f"📍 Expected: ({expected_x:.6f}, {expected_y:.6f})")
            
            coords_match = abs(x - expected_x) < 1e-10 and abs(y - expected_y) < 1e-10
            print(f"✅ Coordinates match: {coords_match}")
            
            return {
                'mode': mode,
                'write_time': write_time,
                'read_time': read_time,
                'wkb_length': len(pickup_wkb),
                'wkb_type': wkb_type,
                'coordinates_match': coords_match,
                'success': True
            }
        else:
            print(f"❌ Invalid WKB length: {len(pickup_wkb)} (expected 21)")
            return {
                'mode': mode,
                'wkb_length': len(pickup_wkb),
                'success': False,
                'error': 'Invalid WKB length'
            }
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return {
            'mode': mode,
            'success': False,
            'error': str(e)
        }

def main():
    print("🔧 WKB FIX VALIDATION TEST")
    print("=" * 50)
    
    # Test both modes
    opensource_result = test_wkb_encoding('opensource', 1000)
    custom_result = test_wkb_encoding('custom', 1000)
    
    # Compare results
    print(f"\n{'='*50}")
    print("📊 WKB FIX COMPARISON")
    print(f"{'='*50}")
    
    print(f"\n🔍 Results:")
    print(f"{'Mode':<15} {'Success':<10} {'WKB Length':<12} {'Coords Match':<15}")
    print("-" * 60)
    
    for result in [opensource_result, custom_result]:
        success = "✅" if result.get('success') else "❌"
        wkb_len = result.get('wkb_length', 'N/A')
        coords_match = "✅" if result.get('coordinates_match') else "❌"
        
        print(f"{result['mode']:<15} {success:<10} {wkb_len:<12} {coords_match:<15}")
        
        if not result.get('success'):
            print(f"  Error: {result.get('error', 'Unknown')}")
    
    # Summary
    if opensource_result.get('success') and custom_result.get('success'):
        if (custom_result.get('wkb_length') == 21 and 
            custom_result.get('coordinates_match') and
            custom_result.get('wkb_type') == 1):
            print("\n🎉 WKB FIX SUCCESSFUL!")
            print("✅ GeoPage encoding now preserves WKB data integrity")
        else:
            print("\n❌ WKB fix incomplete - data integrity issues remain")
    else:
        print("\n❌ WKB fix failed - encoding/decoding errors")

if __name__ == "__main__":
    main()
