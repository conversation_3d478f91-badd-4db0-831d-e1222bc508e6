name: "Code<PERSON>"
run-name: ${{ github.event.inputs.code_scanning_run_name }}
on: [push, pull_request, workflow_dispatch]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  CODE_SCANNING_REF: ${{ github.event.inputs.code_scanning_ref }}
  CODE_SCANNING_BASE_BRANCH: ${{ github.event.inputs.code_scanning_base_branch }}
  CODE_SCANNING_IS_ANALYZING_DEFAULT_BRANCH: ${{ github.event.inputs.code_scanning_is_analyzing_default_branch }}

jobs:
  analyze:
    name: Analyze (${{ matrix.language }})
    runs-on: ${{ fromJSON(matrix.runner) }}
    permissions:
      actions: read
      contents: read
      packages: read
      security-events: write
    continue-on-error: false
    strategy:
      fail-fast: false
      matrix:
        include:
          - language: actions
            category: /language:actions
            build-mode: none
            runner: '["ubuntu-22.04"]'
          - language: go
            category: /language:go
            build-mode: autobuild
            runner: '["ubuntu-22.04"]'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}
          build-mode: ${{ matrix.build-mode }}
          dependency-caching: ${{ runner.environment == 'github-hosted' }}
          queries: "" # Default query suite
          packs: github/ccr-${{ matrix.language }}-queries
          config: |
            default-setup:
              org:
                model-packs: [ ${{ github.event.inputs.code_scanning_codeql_packs }} ]
            threat-models: [  ]
      - name: Setup proxy for registries
        id: proxy
        uses: github/codeql-action/start-proxy@v3
        with:
          registries_credentials: ${{ secrets.GITHUB_REGISTRIES_PROXY }}
          language: ${{ matrix.language }}

      - name: Configure
        uses: github/codeql-action/resolve-environment@v3
        id: resolve-environment
        with:
          language: ${{ matrix.language }}
      - name: Setup Go
        uses: actions/setup-go@v5
        if: matrix.language == 'go' && fromJSON(steps.resolve-environment.outputs.environment).configuration.go.version
        with:
          go-version: ${{ fromJSON(steps.resolve-environment.outputs.environment).configuration.go.version }}
          cache: false

      - name: Autobuild
        uses: github/codeql-action/autobuild@v3

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        env:
          CODEQL_PROXY_HOST: ${{ steps.proxy.outputs.proxy_host }}
          CODEQL_PROXY_PORT: ${{ steps.proxy.outputs.proxy_port }}
          CODEQL_PROXY_CA_CERTIFICATE: ${{ steps.proxy.outputs.proxy_ca_certificate }}
        with:
          category: ${{ matrix.category }}
