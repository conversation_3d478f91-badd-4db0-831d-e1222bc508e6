name: Lint
on:
  push:
  pull_request:

permissions:
  contents: read

jobs:
  lint:
    runs-on: ubuntu-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version-file: 'go.mod'

      - name: Verify dependencies
        run: |
          go mod verify
          go mod download

      - name: Run checks
        run: |
          STATUS=0
          assert-nothing-changed() {
            local diff
            "$@" >/dev/null || return 1
            if ! diff="$(git diff -U1 --color --exit-code)"; then
              printf '\e[31mError: running `\e[1m%s\e[22m` results in modifications that you must check into version control:\e[0m\n%s\n\n' "$*" "$diff" >&2
              git checkout -- .
              STATUS=1
            fi
          }
          assert-nothing-changed go mod tidy
          exit $STATUS

      - name: golangci-lint
        uses: golangci/golangci-lint-action@4afd733a84b1f43292c63897423277bb7f4313a9
        with:
          version: v2.1.6
