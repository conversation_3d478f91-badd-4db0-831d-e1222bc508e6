name: Unit Tests
on: [push, pull_request]

permissions:
  contents: read

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]

    runs-on: ${{ matrix.os }}

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version-file: "go.mod"

      - name: Download dependencies
        run: go mod download

      - name: Run unit tests
        run: go test -race ./...

      - name: Build
        run: go build -v ./cmd/github-mcp-server
