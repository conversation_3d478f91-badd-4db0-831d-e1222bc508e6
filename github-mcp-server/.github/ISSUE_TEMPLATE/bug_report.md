---
name: "\U0001F41B Bug report"
about: Report a bug or unexpected behavior while using GitHub MCP Server
title: ''
labels: bug
assignees: ''

---

### Describe the bug

A clear and concise description of what the bug is.

### Affected version

Please run ` docker run -i --rm ghcr.io/github/github-mcp-server ./github-mcp-server --version` and paste the output below

### Steps to reproduce the behavior

1. Type this '...'
2. View the output '....'
3. See error

### Expected vs actual behavior

A clear and concise description of what you expected to happen and what actually happened.

### Logs

Paste any available logs. Redact if needed.
