# GitHub MCP Server dependencies

The following open source dependencies are used to build the [github/github-mcp-server][] GitHub Model Context Protocol Server.

## Go Packages

Some packages may only be included on certain architectures or operating systems.

{{ range . }}
 - [{{.Name}}](https://pkg.go.dev/{{.Name}}) ([{{.LicenseName}}]({{.LicenseURL}}))
{{- end }}

[github/github-mcp-server]: https://github.com/github/github-mcp-server
