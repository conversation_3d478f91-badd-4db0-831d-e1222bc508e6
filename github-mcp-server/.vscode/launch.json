{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch stdio server",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "cwd": "${workspaceFolder}",
            "program": "cmd/github-mcp-server/main.go",
            "args": ["stdio"],
            "console": "integratedTerminal",
        },
        {
            "name": "Launch stdio server (read-only)",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "cwd": "${workspaceFolder}",
            "program": "cmd/github-mcp-server/main.go",
            "args": ["stdio", "--read-only"],
            "console": "integratedTerminal",
        }
    ]
}