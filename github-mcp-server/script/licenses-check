#!/bin/bash

go install github.com/google/go-licenses@latest

for goos in linux darwin windows ; do
    # Note: we ignore warnings because we want the command to succeed, however the output should be checked
    #       for any new warnings, and potentially we may need to add license information. 
    #
    #       Normally these warnings are packages containing non go code, which may or may not require explicit attribution,
    #       depending on the license.
    GOOS="${goos}" go-licenses report ./... --template .github/licenses.tmpl > third-party-licenses.${goos}.copy.md || echo "Ignore warnings"
    if ! diff -s third-party-licenses.${goos}.copy.md third-party-licenses.${goos}.md; then
        echo "License check failed.\n\nPlease update the license file by running \`.script/licenses\` and committing the output."
        rm -f third-party-licenses.${goos}.copy.md
        exit 1
    fi
    rm -f third-party-licenses.${goos}.copy.md
done



